/* 
 * Agenda Cotte - Main Stylesheet
 */

/* Reset and base styles */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Color variables */
  --color-primary: #4f46e5;
  --color-primary-dark: #4338ca;
  --color-primary-light: #6366f1;
  --color-secondary: #0ea5e9;
  --color-secondary-dark: #0284c7;
  --color-secondary-light: #38bdf8;
  
  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #3b82f6;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  
  /* Border radius */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-normal: all 0.3s ease;
  --transition-fast: all 0.15s ease;
}

html, body {
  font-family: var(--font-sans);
  font-size: 16px;
  line-height: 1.5;
  color: var(--color-gray-900);
  background-color: var(--color-gray-100);
}

a {
  color: var(--color-primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
}

/* Utilities */
.min-h-screen {
  min-height: 100vh;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.text-center {
  text-align: center;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--spacing-4);
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--color-gray-200);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: var(--spacing-4);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Header */
.header {
  background: linear-gradient(to right, var(--color-primary), var(--color-secondary));
  color: white;
  padding: var(--spacing-4) 0;
  box-shadow: var(--shadow-md);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
}

/* Main content */
.main {
  padding: var(--spacing-6) 0;
}

/* Cards */
.card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-6);
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
}

.card-header {
  padding: var(--spacing-4);
  background: linear-gradient(to right, var(--color-primary), var(--color-secondary));
  color: white;
  font-weight: 600;
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}

.card-title {
  font-size: 1.25rem;
  margin: 0;
}

.card-content {
  padding: var(--spacing-4);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  font-size: 1rem;
  font-weight: 500;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition-normal);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-gray-200);
  color: var(--color-gray-800);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-300);
}

.btn-success {
  background-color: var(--color-success);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #16a34a;
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: 0.875rem;
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: 1.125rem;
}

.btn-icon {
  margin-right: var(--spacing-2);
}

/* Form elements */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  color: var(--color-gray-700);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: 1rem;
  line-height: 1.5;
  color: var(--color-gray-900);
  background-color: white;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25);
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.form-check-input {
  margin-right: var(--spacing-2);
}

/* Calendar */
.calendar {
  width: 100%;
  border-collapse: collapse;
}

.calendar-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.calendar-month {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900);
}

.calendar-nav {
  display: flex;
  gap: var(--spacing-2);
}

.calendar-header {
  background-color: var(--color-gray-800);
  color: white;
}

.calendar-header th {
  padding: var(--spacing-3);
  text-align: center;
  font-weight: 600;
}

.calendar-day {
  position: relative;
  height: 120px;
  vertical-align: top;
  border: 1px solid var(--color-gray-300);
  background-color: white;
}

.calendar-day-number {
  display: block;
  padding: var(--spacing-1) var(--spacing-2);
  font-weight: 500;
}

.calendar-day-today {
  background-color: #ebf5ff;
  border: 2px solid var(--color-primary);
}

.calendar-day-other-month {
  background-color: var(--color-gray-50);
  color: var(--color-gray-400);
}

.calendar-day-content {
  padding: 0 var(--spacing-2) var(--spacing-2);
  font-size: 0.875rem;
}

.calendar-event {
  padding: var(--spacing-1) var(--spacing-2);
  margin-bottom: var(--spacing-1);
  border-radius: var(--radius-sm);
  background-color: var(--color-primary-light);
  color: white;
  font-size: 0.75rem;
}

.calendar-event-am {
  background-color: #93c5fd;
  color: #1e3a8a;
}

.calendar-event-pm {
  background-color: #bfdbfe;
  color: #1e3a8a;
}

/* Box styles */
.box-header {
  font-weight: 600;
  margin-bottom: var(--spacing-1);
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  background: linear-gradient(145deg, #f5e6d3, #e8d5b7);
  border: 1px solid #d4c4a8;
}

.box-1 {
  color: #0d47a1;
}

.box-2 {
  color: #1b5e20;
}

/* Mobile calendar */
.mobile-calendar {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.mobile-calendar::-webkit-scrollbar {
  display: none;
}

.mobile-calendar-container {
  display: flex;
  transition: transform 0.3s ease-out;
}

.mobile-calendar-day {
  flex: 0 0 42vw;
  padding: var(--spacing-1);
}

.mobile-calendar-header {
  text-align: center;
  font-weight: 600;
  padding: var(--spacing-2);
  background-color: var(--color-gray-800);
  color: white;
  border-radius: var(--radius);
  margin-bottom: var(--spacing-1);
}

.mobile-calendar-card {
  border: 2px solid var(--color-gray-800);
  border-radius: var(--radius-lg);
  background-color: white;
  height: 100%;
  min-height: 100px;
  padding: var(--spacing-1);
}

.mobile-calendar-day-number {
  font-weight: 600;
  color: var(--color-gray-800);
}

.mobile-calendar-today {
  background-color: #ebf5ff;
  border-color: var(--color-primary);
}

.mobile-calendar-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  padding: var(--spacing-4);
}

.modal-content {
  width: 100%;
  max-width: 500px;
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
}

.modal-header {
  background: linear-gradient(to right, var(--color-primary), var(--color-secondary));
  color: white;
  padding: var(--spacing-4);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.modal-body {
  padding: var(--spacing-4);
}

.modal-footer {
  padding: var(--spacing-4);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2);
  border-top: 1px solid var(--color-gray-200);
}

/* Share options */
.share-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.share-option-whatsapp {
  background-color: #25D366;
  color: white;
}

.share-option-email {
  background-color: #EA4335;
  color: white;
}

.share-option-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-2);
}

.share-preview {
  background-color: var(--color-gray-100);
  padding: var(--spacing-4);
  border-radius: var(--radius);
  margin-bottom: var(--spacing-4);
  white-space: pre-wrap;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  max-height: 200px;
  overflow-y: auto;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .hidden-sm {
    display: none;
  }
  
  .calendar-day {
    height: 80px;
  }
  
  .calendar-event {
    font-size: 0.65rem;
    padding: var(--spacing-1);
  }
  
  .modal-content {
    max-width: 95%;
  }
}

@media (min-width: 641px) {
  .hidden-md {
    display: none;
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-6);
  }
  
  .header-title {
    font-size: 1.75rem;
  }
  
  .card-title {
    font-size: 1.5rem;
  }
}

/* Grid utilities */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }

@media (min-width: 640px) {
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .sm\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
}

/* Flex utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.gap-2 { gap: var(--spacing-2); }
.gap-4 { gap: var(--spacing-4); }

/* Margin utilities */
.mt-2 { margin-top: var(--spacing-2); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mt-4 { margin-top: var(--spacing-4); }
.mb-4 { margin-bottom: var(--spacing-4); }
.my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }

/* Padding utilities */
.p-2 { padding: var(--spacing-2); }
.p-4 { padding: var(--spacing-4); }
.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
.px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }

/* Text utilities */
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--color-primary); }
.text-danger { color: var(--color-danger); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-700 { color: var(--color-gray-700); }