<?php
/**
 * API endpoints for Appointments
 */

require_once dirname(__DIR__) . '/includes/config.php';
require_once INCLUDES_PATH . '/models/Appointment.php';
require_once INCLUDES_PATH . '/models/Box.php';
require_once INCLUDES_PATH . '/models/Professional.php';

// Set headers for API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Initialize models
$appointmentModel = new Appointment();
$boxModel = new Box();
$professionalModel = new Professional();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle requests based on method
switch ($method) {
    case 'GET':
        // Check for month query param
        if (isset($_GET['year']) && isset($_GET['month'])) {
            $year = (int) $_GET['year'];
            $month = (int) $_GET['month'];
            
            // Validate year and month
            if ($year < 2000 || $year > 2100 || $month < 1 || $month > 12) {
                json_response(['success' => false, 'message' => 'Año o mes inválido'], 400);
            }
            
            // Get appointments for month
            $appointments = $appointmentModel->getAppointmentsByMonth($year, $month);
            json_response(['success' => true, 'data' => $appointments]);
        }
        // Check for date query param
        else if (isset($_GET['date'])) {
            $date = $_GET['date'];
            
            // Validate date
            if (!validate_date($date)) {
                json_response(['success' => false, 'message' => 'Formato de fecha inválido'], 400);
            }
            
            // Get appointments for date
            $appointments = $appointmentModel->getAppointmentsByDate($date);
            json_response(['success' => true, 'data' => $appointments]);
        }
        // Get all appointments if no specific query
        else {
            $appointments = $appointmentModel->getAllAppointments();
            json_response(['success' => true, 'data' => $appointments]);
        }
        break;
        
    case 'POST':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['date']) || empty($data['date'])) {
            json_response(['success' => false, 'message' => 'La fecha es requerida'], 400);
        }
        
        if (!validate_date($data['date'])) {
            json_response(['success' => false, 'message' => 'Formato de fecha inválido'], 400);
        }
        
        if (!isset($data['box_id']) || !is_numeric($data['box_id'])) {
            json_response(['success' => false, 'message' => 'Box inválido'], 400);
        }
        
        if (!isset($data['shift']) || ($data['shift'] !== 'AM' && $data['shift'] !== 'PM')) {
            json_response(['success' => false, 'message' => 'Turno inválido'], 400);
        }
        
        if (!isset($data['professional_id']) || !is_numeric($data['professional_id'])) {
            json_response(['success' => false, 'message' => 'Profesional inválido'], 400);
        }
        
        $date = $data['date'];
        $boxId = (int) $data['box_id'];
        $shift = $data['shift'];
        $professionalId = (int) $data['professional_id'];
        
        // Check if box exists
        $box = $boxModel->getBoxById($boxId);
        if (!$box) {
            json_response(['success' => false, 'message' => 'Box no encontrado'], 404);
        }
        
        // Check if professional exists
        $professional = $professionalModel->getProfessionalById($professionalId);
        if (!$professional) {
            json_response(['success' => false, 'message' => 'Profesional no encontrado'], 404);
        }
        
        // Check if professional is available for the date (not on leave)
        if ($professional['status'] !== 'Vigente') {
            if ($professional['status_from'] && $professional['status_to']) {
                $appointmentDate = strtotime($date);
                $statusFrom = strtotime($professional['status_from']);
                $statusTo = strtotime($professional['status_to']);
                
                if ($appointmentDate >= $statusFrom && $appointmentDate <= $statusTo) {
                    json_response([
                        'success' => false, 
                        'message' => 'Profesional no disponible por ' . $professional['status']
                    ], 400);
                }
            }
        }
        
        // Check for professional conflict
        $conflict = $appointmentModel->checkProfessionalConflict($professionalId, $date, $shift);
        if ($conflict) {
            json_response([
                'success' => false, 
                'message' => 'El profesional ya está asignado a otro box en este turno',
                'conflict' => [
                    'box' => $conflict['box_name'],
                    'shift' => $conflict['shift']
                ]
            ], 400);
        }
        
        // Add appointment
        $appointmentId = $appointmentModel->addAppointment($date, $boxId, $shift, $professionalId);
        
        if ($appointmentId) {
            $appointment = $appointmentModel->getAppointmentById($appointmentId);
            json_response(['success' => true, 'message' => 'Cita agendada exitosamente', 'data' => $appointment]);
        } else {
            json_response(['success' => false, 'message' => 'Error al agendar la cita'], 500);
        }
        break;
        
    case 'PUT':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            json_response(['success' => false, 'message' => 'ID de cita inválido'], 400);
        }
        
        if (!isset($data['date']) || empty($data['date'])) {
            json_response(['success' => false, 'message' => 'La fecha es requerida'], 400);
        }
        
        if (!validate_date($data['date'])) {
            json_response(['success' => false, 'message' => 'Formato de fecha inválido'], 400);
        }
        
        if (!isset($data['box_id']) || !is_numeric($data['box_id'])) {
            json_response(['success' => false, 'message' => 'Box inválido'], 400);
        }
        
        if (!isset($data['shift']) || ($data['shift'] !== 'AM' && $data['shift'] !== 'PM')) {
            json_response(['success' => false, 'message' => 'Turno inválido'], 400);
        }
        
        if (!isset($data['professional_id']) || !is_numeric($data['professional_id'])) {
            json_response(['success' => false, 'message' => 'Profesional inválido'], 400);
        }
        
        $id = (int) $data['id'];
        $date = $data['date'];
        $boxId = (int) $data['box_id'];
        $shift = $data['shift'];
        $professionalId = (int) $data['professional_id'];
        
        // Check if appointment exists
        $appointment = $appointmentModel->getAppointmentById($id);
        if (!$appointment) {
            json_response(['success' => false, 'message' => 'Cita no encontrada'], 404);
        }
        
        // Check if box exists
        $box = $boxModel->getBoxById($boxId);
        if (!$box) {
            json_response(['success' => false, 'message' => 'Box no encontrado'], 404);
        }
        
        // Check if professional exists
        $professional = $professionalModel->getProfessionalById($professionalId);
        if (!$professional) {
            json_response(['success' => false, 'message' => 'Profesional no encontrado'], 404);
        }
        
        // Check if professional is available for the date (not on leave)
        if ($professional['status'] !== 'Vigente') {
            if ($professional['status_from'] && $professional['status_to']) {
                $appointmentDate = strtotime($date);
                $statusFrom = strtotime($professional['status_from']);
                $statusTo = strtotime($professional['status_to']);
                
                if ($appointmentDate >= $statusFrom && $appointmentDate <= $statusTo) {
                    json_response([
                        'success' => false, 
                        'message' => 'Profesional no disponible por ' . $professional['status']
                    ], 400);
                }
            }
        }
        
        // Check for professional conflict
        if ($professionalId != $appointment['professional_id'] || 
            $date != $appointment['appointment_date'] || 
            $shift != $appointment['shift']) {
            
            $conflict = $appointmentModel->checkProfessionalConflict($professionalId, $date, $shift);
            if ($conflict) {
                json_response([
                    'success' => false, 
                    'message' => 'El profesional ya está asignado a otro box en este turno',
                    'conflict' => [
                        'box' => $conflict['box_name'],
                        'shift' => $conflict['shift']
                    ]
                ], 400);
            }
        }
        
        // Update appointment
        if ($appointmentModel->updateAppointment($id, $date, $boxId, $shift, $professionalId)) {
            $updatedAppointment = $appointmentModel->getAppointmentById($id);
            json_response(['success' => true, 'message' => 'Cita actualizada exitosamente', 'data' => $updatedAppointment]);
        } else {
            json_response(['success' => false, 'message' => 'Error al actualizar la cita'], 500);
        }
        break;
        
    case 'DELETE':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            json_response(['success' => false, 'message' => 'ID de cita inválido'], 400);
        }
        
        $id = (int) $data['id'];
        
        // Check if appointment exists
        $appointment = $appointmentModel->getAppointmentById($id);
        if (!$appointment) {
            json_response(['success' => false, 'message' => 'Cita no encontrada'], 404);
        }
        
        // Delete appointment
        if ($appointmentModel->deleteAppointment($id)) {
            json_response(['success' => true, 'message' => 'Cita eliminada exitosamente']);
        } else {
            json_response(['success' => false, 'message' => 'Error al eliminar la cita'], 500);
        }
        break;
        
    default:
        json_response(['success' => false, 'message' => 'Método no permitido'], 405);
}