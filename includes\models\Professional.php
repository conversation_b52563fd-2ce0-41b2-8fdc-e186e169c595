<?php
/**
 * Professional Model for Agenda Cotte
 */

class Professional {
    private $conn;
    
    /**
     * Constructor - Initialize database connection
     */
    public function __construct() {
        $this->conn = get_db_connection();
    }
    
    /**
     * Destructor - Close database connection
     */
    public function __destruct() {
        close_db_connection($this->conn);
    }
    
    /**
     * Get all professionals
     * 
     * @return array List of all professionals
     */
    public function getAllProfessionals() {
        $professionals = [];
        
        $sql = "SELECT * FROM professionals ORDER BY name";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $professionals[] = $row;
            }
        }
        
        return $professionals;
    }
    
    /**
     * Get active professionals for a specific date
     * 
     * @param string $date Date to check (YYYY-MM-DD)
     * @return array List of active professionals for the date
     */
    public function getActiveProfessionalsByDate($date) {
        $professionals = [];
        
        $sql = "SELECT * FROM professionals WHERE 
                status = 'Vigente' OR 
                (status != 'Vigente' AND (
                    status_from IS NULL OR 
                    status_to IS NULL OR 
                    ? < status_from OR 
                    ? > status_to
                ))
                ORDER BY name";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ss", $date, $date);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $professionals[] = $row;
            }
        }
        
        return $professionals;
    }
    
    /**
     * Get professional by ID
     * 
     * @param int $id Professional ID
     * @return array|null Professional data or null if not found
     */
    public function getProfessionalById($id) {
        $sql = "SELECT * FROM professionals WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    /**
     * Add new professional
     * 
     * @param string $name Professional name
     * @param string $status Status (default 'Vigente')
     * @param string|null $statusFrom Status from date (YYYY-MM-DD)
     * @param string|null $statusTo Status to date (YYYY-MM-DD)
     * @return int|bool New professional ID or false on error
     */
    public function addProfessional($name, $status = 'Vigente', $statusFrom = null, $statusTo = null) {
        $sql = "INSERT INTO professionals (name, status, status_from, status_to) VALUES (?, ?, ?, ?)";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssss", $name, $status, $statusFrom, $statusTo);
        
        if ($stmt->execute()) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update professional
     * 
     * @param int $id Professional ID
     * @param string $name Professional name
     * @param string $status Status
     * @param string|null $statusFrom Status from date (YYYY-MM-DD)
     * @param string|null $statusTo Status to date (YYYY-MM-DD)
     * @return bool True on success, false on error
     */
    public function updateProfessional($id, $name, $status = 'Vigente', $statusFrom = null, $statusTo = null) {
        $sql = "UPDATE professionals SET name = ?, status = ?, status_from = ?, status_to = ? WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssssi", $name, $status, $statusFrom, $statusTo, $id);
        
        return $stmt->execute();
    }
    
    /**
     * Update professional status
     * 
     * @param int $id Professional ID
     * @param string $status New status
     * @param string|null $statusFrom Status from date (YYYY-MM-DD)
     * @param string|null $statusTo Status to date (YYYY-MM-DD)
     * @return bool True on success, false on error
     */
    public function updateProfessionalStatus($id, $status, $statusFrom = null, $statusTo = null) {
        $sql = "UPDATE professionals SET status = ?, status_from = ?, status_to = ? WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("sssi", $status, $statusFrom, $statusTo, $id);
        
        return $stmt->execute();
    }
    
    /**
     * Delete professional
     * 
     * @param int $id Professional ID
     * @return bool True on success, false on error
     */
    public function deleteProfessional($id) {
        $sql = "DELETE FROM professionals WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        
        return $stmt->execute();
    }
    
    /**
     * Check if professional name exists
     * 
     * @param string $name Professional name
     * @param int|null $excludeId Exclude professional ID (for updates)
     * @return bool True if exists, false otherwise
     */
    public function professionalNameExists($name, $excludeId = null) {
        if ($excludeId !== null) {
            $sql = "SELECT COUNT(*) AS count FROM professionals WHERE name = ? AND id != ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("si", $name, $excludeId);
        } else {
            $sql = "SELECT COUNT(*) AS count FROM professionals WHERE name = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("s", $name);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['count'] > 0;
    }
}