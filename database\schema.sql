-- Agenda Cotte Database Schema

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS agenda_cotte;
USE agenda_cotte;

-- Create boxes table
CREATE TABLE IF NOT EXISTS boxes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create professionals table
CREATE TABLE IF NOT EXISTS professionals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    status ENUM('Vigente', 'Licencia médica', 'Permiso administrativo', 'Vacaciones', 'Cursos') DEFAULT 'Vigente',
    status_from DATE NULL,
    status_to DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    appointment_date DATE NOT NULL,
    box_id INT NOT NULL,
    shift ENUM('AM', 'PM') NOT NULL,
    professional_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
    FOREIGN KEY (professional_id) REFERENCES professionals(id) ON DELETE CASCADE,
    UNIQUE KEY unique_appointment (appointment_date, box_id, shift)
);

-- Insert default boxes
INSERT INTO boxes (name) VALUES ('Box 1'), ('Box 2');

-- Insert default professionals
INSERT INTO professionals (name, status) VALUES 
('Dr. Pérez', 'Vigente'),
('Dra. García', 'Vigente'),
('Dr. López', 'Vigente');