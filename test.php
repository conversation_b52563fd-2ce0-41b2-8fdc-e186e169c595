<?php
/**
 * Simple test script for Agenda Cotte
 * 
 * This script tests database connectivity and API endpoints.
 */

// Load configuration
require_once __DIR__ . '/includes/config.php';

// Test results array
$testResults = [];

// Test database connection
function testDatabaseConnection() {
    $conn = get_db_connection();
    
    if ($conn === false) {
        return [
            'name' => 'Database Connection',
            'status' => 'FAILED',
            'message' => 'Could not connect to the database. Check your connection settings.'
        ];
    }
    
    $conn->close();
    
    return [
        'name' => 'Database Connection',
        'status' => 'PASSED',
        'message' => 'Successfully connected to the database.'
    ];
}

// Test API endpoint
function testApiEndpoint($endpoint, $method = 'GET', $data = null) {
    $url = "http://{$_SERVER['HTTP_HOST']}" . 
           rtrim(dirname($_SERVER['PHP_SELF']), '/') . 
           "/api/{$endpoint}.php";
    
    $ch = curl_init();
    
    $options = [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => ['Content-Type: application/json']
    ];
    
    if ($method === 'POST' || $method === 'PUT' || $method === 'DELETE') {
        $options[CURLOPT_CUSTOMREQUEST] = $method;
        
        if ($data !== null) {
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
        }
    }
    
    curl_setopt_array($ch, $options);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return [
            'name' => "API Endpoint: {$endpoint}",
            'status' => 'FAILED',
            'message' => "cURL Error: {$error}"
        ];
    }
    
    if ($httpCode >= 400) {
        return [
            'name' => "API Endpoint: {$endpoint}",
            'status' => 'FAILED',
            'message' => "HTTP Error: {$httpCode}"
        ];
    }
    
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'name' => "API Endpoint: {$endpoint}",
            'status' => 'FAILED',
            'message' => 'Invalid JSON response: ' . json_last_error_msg()
        ];
    }
    
    return [
        'name' => "API Endpoint: {$endpoint}",
        'status' => 'PASSED',
        'message' => "Successfully accessed API endpoint."
    ];
}

// Run tests
$testResults[] = testDatabaseConnection();
$testResults[] = testApiEndpoint('boxes');
$testResults[] = testApiEndpoint('professionals');
$testResults[] = testApiEndpoint('appointments');

// Count passed and failed tests
$passedTests = 0;
$failedTests = 0;

foreach ($testResults as $result) {
    if ($result['status'] === 'PASSED') {
        $passedTests++;
    } else {
        $failedTests++;
    }
}

// Output test results as HTML
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agenda Cotte - Test Results</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        
        h1 {
            color: #4f46e5;
            border-bottom: 2px solid #4f46e5;
            padding-bottom: 10px;
        }
        
        .summary {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .test-results {
            margin-top: 30px;
        }
        
        .test-result {
            background-color: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .test-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .test-status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        .passed {
            background-color: #ecfdf5;
            color: #047857;
        }
        
        .failed {
            background-color: #fef2f2;
            color: #b91c1c;
        }
        
        .test-message {
            color: #6b7280;
            font-size: 14px;
        }
        
        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #4f46e5;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>Agenda Cotte - Test Results</h1>
    
    <div class="summary">
        <p><strong>Total Tests:</strong> <?php echo count($testResults); ?></p>
        <p><strong>Passed Tests:</strong> <?php echo $passedTests; ?></p>
        <p><strong>Failed Tests:</strong> <?php echo $failedTests; ?></p>
    </div>
    
    <div class="test-results">
        <?php foreach ($testResults as $result): ?>
            <div class="test-result">
                <div class="test-name"><?php echo htmlspecialchars($result['name']); ?></div>
                <div class="test-status <?php echo strtolower($result['status']); ?>">
                    <?php echo $result['status']; ?>
                </div>
                <div class="test-message"><?php echo htmlspecialchars($result['message']); ?></div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <a href="index.php" class="back-link">← Volver a la aplicación</a>
</body>
</html>