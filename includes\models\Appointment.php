<?php
/**
 * Appointment Model for Agenda Cotte
 */

class Appointment {
    private $conn;
    
    /**
     * Constructor - Initialize database connection
     */
    public function __construct() {
        $this->conn = get_db_connection();
    }
    
    /**
     * Destructor - Close database connection
     */
    public function __destruct() {
        close_db_connection($this->conn);
    }
    
    /**
     * Get all appointments
     * 
     * @return array List of all appointments with related data
     */
    public function getAllAppointments() {
        $appointments = [];
        
        $sql = "SELECT a.*, b.name as box_name, p.name as professional_name 
                FROM appointments a
                INNER JOIN boxes b ON a.box_id = b.id
                INNER JOIN professionals p ON a.professional_id = p.id
                ORDER BY a.appointment_date DESC";
                
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $appointments[] = $row;
            }
        }
        
        return $appointments;
    }
    
    /**
     * Get appointments for a specific month
     * 
     * @param int $year Year
     * @param int $month Month (1-12)
     * @return array List of appointments for the month with related data
     */
    public function getAppointmentsByMonth($year, $month) {
        $appointments = [];
        
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));
        
        $sql = "SELECT a.*, b.name as box_name, p.name as professional_name 
                FROM appointments a
                INNER JOIN boxes b ON a.box_id = b.id
                INNER JOIN professionals p ON a.professional_id = p.id
                WHERE a.appointment_date BETWEEN ? AND ?
                ORDER BY a.appointment_date";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ss", $startDate, $endDate);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $appointments[] = $row;
            }
        }
        
        return $appointments;
    }
    
    /**
     * Get appointments for a specific date
     * 
     * @param string $date Date (YYYY-MM-DD)
     * @return array List of appointments for the date with related data
     */
    public function getAppointmentsByDate($date) {
        $appointments = [];
        
        $sql = "SELECT a.*, b.name as box_name, p.name as professional_name 
                FROM appointments a
                INNER JOIN boxes b ON a.box_id = b.id
                INNER JOIN professionals p ON a.professional_id = p.id
                WHERE a.appointment_date = ?
                ORDER BY a.box_id, a.shift";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $date);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $appointments[] = $row;
            }
        }
        
        return $appointments;
    }
    
    /**
     * Get appointment by ID
     * 
     * @param int $id Appointment ID
     * @return array|null Appointment data or null if not found
     */
    public function getAppointmentById($id) {
        $sql = "SELECT a.*, b.name as box_name, p.name as professional_name 
                FROM appointments a
                INNER JOIN boxes b ON a.box_id = b.id
                INNER JOIN professionals p ON a.professional_id = p.id
                WHERE a.id = ?";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    /**
     * Check if professional is already booked for the specified date and shift
     * 
     * @param int $professionalId Professional ID
     * @param string $date Date (YYYY-MM-DD)
     * @param string $shift Shift (AM/PM)
     * @return array|null Appointment data if found, null otherwise
     */
    public function checkProfessionalConflict($professionalId, $date, $shift) {
        $sql = "SELECT a.*, b.name as box_name
                FROM appointments a
                INNER JOIN boxes b ON a.box_id = b.id
                WHERE a.professional_id = ? AND a.appointment_date = ? AND a.shift = ?";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("iss", $professionalId, $date, $shift);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    /**
     * Add new appointment
     * 
     * @param string $date Appointment date (YYYY-MM-DD)
     * @param int $boxId Box ID
     * @param string $shift Shift (AM/PM)
     * @param int $professionalId Professional ID
     * @return int|bool New appointment ID or false on error
     */
    public function addAppointment($date, $boxId, $shift, $professionalId) {
        $sql = "INSERT INTO appointments (appointment_date, box_id, shift, professional_id) 
                VALUES (?, ?, ?, ?)";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("sisi", $date, $boxId, $shift, $professionalId);
        
        if ($stmt->execute()) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update appointment
     * 
     * @param int $id Appointment ID
     * @param string $date Appointment date (YYYY-MM-DD)
     * @param int $boxId Box ID
     * @param string $shift Shift (AM/PM)
     * @param int $professionalId Professional ID
     * @return bool True on success, false on error
     */
    public function updateAppointment($id, $date, $boxId, $shift, $professionalId) {
        $sql = "UPDATE appointments 
                SET appointment_date = ?, box_id = ?, shift = ?, professional_id = ? 
                WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("sisii", $date, $boxId, $shift, $professionalId, $id);
        
        return $stmt->execute();
    }
    
    /**
     * Delete appointment
     * 
     * @param int $id Appointment ID
     * @return bool True on success, false on error
     */
    public function deleteAppointment($id) {
        $sql = "DELETE FROM appointments WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        
        return $stmt->execute();
    }
    
    /**
     * Delete appointments for a specific box
     * 
     * @param int $boxId Box ID
     * @param string|null $fromDate From date (YYYY-MM-DD), null for all dates
     * @return bool True on success, false on error
     */
    public function deleteAppointmentsByBox($boxId, $fromDate = null) {
        if ($fromDate !== null) {
            $sql = "DELETE FROM appointments WHERE box_id = ? AND appointment_date >= ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("is", $boxId, $fromDate);
        } else {
            $sql = "DELETE FROM appointments WHERE box_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("i", $boxId);
        }
        
        return $stmt->execute();
    }
    
    /**
     * Delete appointments for a specific professional
     * 
     * @param int $professionalId Professional ID
     * @param string|null $fromDate From date (YYYY-MM-DD), null for all dates
     * @param string|null $toDate To date (YYYY-MM-DD), null for open end
     * @return bool True on success, false on error
     */
    public function deleteAppointmentsByProfessional($professionalId, $fromDate = null, $toDate = null) {
        if ($fromDate !== null && $toDate !== null) {
            $sql = "DELETE FROM appointments 
                    WHERE professional_id = ? 
                    AND appointment_date >= ? 
                    AND appointment_date <= ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("iss", $professionalId, $fromDate, $toDate);
        } else if ($fromDate !== null) {
            $sql = "DELETE FROM appointments 
                    WHERE professional_id = ? 
                    AND appointment_date >= ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("is", $professionalId, $fromDate);
        } else {
            $sql = "DELETE FROM appointments WHERE professional_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("i", $professionalId);
        }
        
        return $stmt->execute();
    }
    
    /**
     * Update box name in appointments
     * 
     * @param int $oldBoxId Old box ID
     * @param int $newBoxId New box ID
     * @param string|null $fromDate From date (YYYY-MM-DD), null for all dates
     * @return bool True on success, false on error
     */
    public function updateAppointmentsBoxName($oldBoxId, $newBoxId, $fromDate = null) {
        if ($fromDate !== null) {
            $sql = "UPDATE appointments 
                    SET box_id = ? 
                    WHERE box_id = ? 
                    AND appointment_date >= ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("iis", $newBoxId, $oldBoxId, $fromDate);
        } else {
            $sql = "UPDATE appointments 
                    SET box_id = ? 
                    WHERE box_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("ii", $newBoxId, $oldBoxId);
        }
        
        return $stmt->execute();
    }
}