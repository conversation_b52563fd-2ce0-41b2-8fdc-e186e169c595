/**
 * Agenda Cotte - Main Application JavaScript
 */

// Constants and configuration
const API_URL = './api';
const MONTHS = [
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
    '<PERSON>', 'A<PERSON><PERSON>', 'Septiembre', 'Octubre', '<PERSON><PERSON><PERSON>', 'Dicie<PERSON>'
];
const DAY_NAMES = ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Viernes', 'S<PERSON>bado'];
const DAY_NAMES_SHORT = ['Dom', 'Lun', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>áb'];

// Main App Class
class AgendaApp {
    constructor() {
        this.currentDate = new Date();
        this.selectedDate = null;
        this.appointments = [];
        this.boxes = [];
        this.professionals = [];
        this.isMobile = window.innerWidth < 640;
        
        // Modal states
        this.modalStates = {
            appointment: false,
            boxAdd: false,
            boxDelete: false,
            boxEdit: false,
            professionalAdd: false,
            professionalDelete: false,
            professionalStatus: false,
            conflict: false,
            shareOptions: false,
            sharePreview: false
        };
        
        // Form data
        this.formData = {
            appointment: {
                id: null,
                date: '',
                box_id: '',
                shift: '',
                professional_id: ''
            },
            box: {
                id: null,
                name: ''
            },
            professional: {
                id: null,
                name: '',
                status: 'Vigente',
                status_from: '',
                status_to: ''
            },
            conflict: {
                professional: '',
                box: '',
                shift: ''
            },
            share: {
                date: null,
                method: 'whatsapp',
                content: ''
            }
        };
        
        // Mobile calendar state
        this.mobileCalendar = {
            scrollPosition: 0,
            maxScroll: 0,
            touchStartX: 0,
            touchEndX: 0,
            isScrolling: false
        };
        
        // Initialize the application
        this.init();
    }
    
    /**
     * Initialize the application
     */
    async init() {
        this.appElement = document.getElementById('app');
        
        // Render initial loading state
        this.renderLoading();
        
        try {
            // Fetch initial data
            await Promise.all([
                this.fetchBoxes(),
                this.fetchProfessionals(),
                this.fetchAppointmentsByMonth(
                    this.currentDate.getFullYear(), 
                    this.currentDate.getMonth() + 1
                )
            ]);
            
            // Initial render
            this.render();
            
            // Add window resize listener for responsive behavior
            window.addEventListener('resize', () => {
                const wasMobile = this.isMobile;
                this.isMobile = window.innerWidth < 640;
                
                // Only re-render if mobile state changed
                if (wasMobile !== this.isMobile) {
                    this.render();
                }
            });
            
        } catch (error) {
            console.error('Error initializing application:', error);
            this.renderError('Error al cargar la aplicación. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Render the application
     */
    render() {
        // Skip render if we're showing a modal
        if (Object.values(this.modalStates).some(state => state)) {
            return;
        }
        
        this.appElement.innerHTML = `
            <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4">
                <div class="container">
                    <header class="card mb-4">
                        <div class="card-header">
                            <h1 class="card-title text-center">Sistema de Calendario y Agendamiento</h1>
                        </div>
                    </header>
                    
                    <main>
                        <div class="card">
                            <div class="card-content">
                                <!-- Controls for managing boxes and professionals -->
                                <div class="flex flex-col sm:flex-row items-center justify-between mb-4 gap-4">
                                    <!-- Box controls -->
                                    <div class="flex flex-col sm:flex-row items-center gap-2">
                                        <span class="font-bold">Box:</span>
                                        <div class="relative">
                                            <button class="btn btn-secondary" id="box-options-btn">
                                                Opciones
                                            </button>
                                            <div class="hidden absolute top-full left-0 mt-1 w-52 bg-white border border-gray-300 shadow-lg z-10 rounded" id="box-options-menu">
                                                <button class="w-full text-left px-4 py-2 hover:bg-gray-100" id="add-box-btn">
                                                    <i class="fas fa-plus mr-2"></i> Agregar box
                                                </button>
                                                <button class="w-full text-left px-4 py-2 hover:bg-gray-100" id="delete-box-btn">
                                                    <i class="fas fa-trash mr-2"></i> Eliminar box
                                                </button>
                                                <button class="w-full text-left px-4 py-2 hover:bg-gray-100" id="edit-box-btn">
                                                    <i class="fas fa-edit mr-2"></i> Editar box
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Month selector -->
                                    <div class="flex items-center">
                                        <button class="btn btn-secondary" id="prev-month-btn">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                        <h2 class="mx-2 font-bold text-xl text-center min-w-[160px]">
                                            ${MONTHS[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}
                                        </h2>
                                        <button class="btn btn-secondary" id="next-month-btn">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Professional controls -->
                                    <div class="flex flex-col sm:flex-row items-center gap-2">
                                        <span class="font-bold">Profesionales:</span>
                                        <div class="relative">
                                            <button class="btn btn-secondary" id="professional-options-btn">
                                                Opciones
                                            </button>
                                            <div class="hidden absolute top-full right-0 mt-1 w-60 bg-white border border-gray-300 shadow-lg z-10 rounded" id="professional-options-menu">
                                                <button class="w-full text-left px-4 py-2 hover:bg-gray-100" id="add-professional-btn">
                                                    <i class="fas fa-plus mr-2"></i> Agregar profesional
                                                </button>
                                                <button class="w-full text-left px-4 py-2 hover:bg-gray-100" id="delete-professional-btn">
                                                    <i class="fas fa-trash mr-2"></i> Eliminar profesional
                                                </button>
                                                <button class="w-full text-left px-4 py-2 hover:bg-gray-100" id="edit-status-btn">
                                                    <i class="fas fa-edit mr-2"></i> Cambiar estado
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Mobile calendar view -->
                                ${this.isMobile ? this.renderMobileCalendar() : ''}
                                
                                <!-- Desktop calendar view -->
                                ${!this.isMobile ? this.renderDesktopCalendar() : ''}
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        `;
        
        // Add event listeners
        this.addEventListeners();
    }
    
    /**
     * Render the mobile calendar view
     */
    renderMobileCalendar() {
        const weekDays = DAY_NAMES.filter((_, index) => index !== 0 && index !== 6); // Exclude weekends
        const weeks = this.getWeeksForMobile();
        
        return `
            <div class="mobile-calendar-nav">
                <button class="btn btn-secondary btn-sm" id="mobile-prev-btn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="text-sm font-medium text-gray-600">
                    Desliza para ver más días
                </span>
                <button class="btn btn-secondary btn-sm" id="mobile-next-btn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            
            <div class="mobile-calendar" id="mobile-calendar">
                <div class="mobile-calendar-container" id="mobile-calendar-container" 
                     style="transform: translateX(-${this.mobileCalendar.scrollPosition}px)">
                    ${weekDays.map(day => `
                        <div class="mobile-calendar-day">
                            <div class="mobile-calendar-header">${day}</div>
                            ${weeks.map((week, weekIndex) => {
                                const dayInWeek = week.find(d => d.dayOfWeekIndex === weekDays.indexOf(day));
                                
                                if (!dayInWeek) {
                                    return `
                                        <div class="mb-1">
                                            <div class="mobile-calendar-card"></div>
                                        </div>
                                    `;
                                }
                                
                                const dayData = dayInWeek.dayData;
                                const isToday = dayData.date ? this.isToday(dayData.date) : false;
                                
                                return `
                                    <div class="mb-1">
                                        <div class="mobile-calendar-card ${isToday ? 'mobile-calendar-today' : ''}" 
                                             data-date="${dayData.date ? this.formatDate(dayData.date) : ''}"
                                             ${dayData.date ? 'id="day-' + this.formatDate(dayData.date) + '"' : ''}>
                                            ${dayData.date ? `
                                                <div class="flex justify-between items-start mb-1">
                                                    <div class="mobile-calendar-day-number">${dayData.dayNumber}</div>
                                                    <button class="text-gray-400 hover:text-gray-600 transition-colors p-1 share-day-btn"
                                                            data-date="${this.formatDate(dayData.date)}"
                                                            title="Compartir día">
                                                        <i class="fas fa-share-alt"></i>
                                                    </button>
                                                </div>
                                                ${this.renderDayAppointments(dayData.date, true)}
                                            ` : ''}
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * Render the desktop calendar view
     */
    renderDesktopCalendar() {
        const days = this.getDaysInMonth(this.currentDate);
        
        return `
            <div class="grid grid-cols-5 gap-1 bg-gray-800 p-1 rounded-lg">
                <!-- Day headers -->
                ${DAY_NAMES.filter((_, index) => index !== 0 && index !== 6) // Exclude weekends
                        .map(day => `
                    <div class="text-center font-bold p-4 bg-gray-900 text-white rounded">${day}</div>
                `).join('')}
                
                <!-- Calendar days -->
                ${days.map((dayData, index) => {
                    if (!dayData.date || !dayData.dayNumber) {
                        return `<div class="p-2"></div>`;
                    }
                    
                    const dayOfWeek = dayData.date.getDay();
                    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
                    
                    // Skip weekends
                    if (isWeekend) {
                        return '';
                    }
                    
                    const isToday = this.isToday(dayData.date);
                    
                    return `
                        <div class="calendar-day ${isToday ? 'calendar-day-today' : ''}"
                             data-date="${this.formatDate(dayData.date)}"
                             id="day-${this.formatDate(dayData.date)}">
                            <div class="flex justify-between items-start p-2">
                                <span class="calendar-day-number">${dayData.dayNumber}</span>
                                <button class="text-gray-400 hover:text-gray-600 transition-colors p-1 share-day-btn"
                                        data-date="${this.formatDate(dayData.date)}"
                                        title="Compartir día">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                            </div>
                            <div class="calendar-day-content">
                                ${this.renderDayAppointments(dayData.date)}
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }
    
    /**
     * Render appointments for a specific day
     * 
     * @param {Date} date - The date to render appointments for
     * @param {boolean} isMobile - Whether to render for mobile view
     * @returns {string} HTML content
     */
    renderDayAppointments(date, isMobile = false) {
        const dayAppointments = this.getAppointmentsForDate(date);
        let html = '';
        
        // Organize appointments by box
        const boxAppointments = {};
        
        this.boxes.forEach(box => {
            boxAppointments[box.name] = {
                AM: null,
                PM: null
            };
        });
        
        dayAppointments.forEach(appointment => {
            const boxName = this.boxes.find(b => b.id == appointment.box_id)?.name;
            const professionalName = this.professionals.find(p => p.id == appointment.professional_id)?.name;
            
            if (boxName && professionalName) {
                boxAppointments[boxName][appointment.shift] = professionalName;
            }
        });
        
        // Render each box with appointments
        Object.entries(boxAppointments).forEach(([boxName, shifts], index) => {
            const boxIndex = index + 1;
            const hasAppointments = shifts.AM || shifts.PM;
            
            if (hasAppointments) {
                const boxClass = `box-${boxIndex}`;
                
                html += `
                    <div class="mb-1">
                        <div class="box-header ${boxClass}">
                            ${boxName}
                        </div>
                `;
                
                if (shifts.AM) {
                    html += `
                        <div class="calendar-event calendar-event-am">
                            AM: ${shifts.AM}
                        </div>
                    `;
                }
                
                if (shifts.PM) {
                    html += `
                        <div class="calendar-event calendar-event-pm">
                            PM: ${shifts.PM}
                        </div>
                    `;
                }
                
                html += `</div>`;
            }
        });
        
        return html;
    }
    
    /**
     * Render loading state
     */
    renderLoading() {
        this.appElement.innerHTML = `
            <div class="loading">
                <div class="loading-spinner"></div>
                <p>Cargando aplicación...</p>
            </div>
        `;
    }
    
    /**
     * Render error message
     * 
     * @param {string} message - Error message to display
     */
    renderError(message) {
        this.appElement.innerHTML = `
            <div class="loading">
                <div class="text-danger mb-4">
                    <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                    <p>${message}</p>
                </div>
                <button class="btn btn-primary" id="retry-btn">
                    Reintentar
                </button>
            </div>
        `;
        
        document.getElementById('retry-btn').addEventListener('click', () => {
            this.init();
        });
    }
    
    /**
     * Render appointment modal
     */
    renderAppointmentModal() {
        const modalContent = `
            <div class="modal" id="appointment-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">Sistema de Agendamiento</h2>
                    </div>
                    <div class="modal-body">
                        <form id="appointment-form">
                            <div class="form-group">
                                <label class="form-label" for="box">BOX:</label>
                                <select class="form-control form-select" id="box" name="box" required>
                                    <option value="">Seleccionar Box</option>
                                    ${this.boxes.map(box => `
                                        <option value="${box.id}" ${this.formData.appointment.box_id == box.id ? 'selected' : ''}>
                                            ${box.name}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="shift">TURNO:</label>
                                <select class="form-control form-select" id="shift" name="shift" required>
                                    <option value="">Seleccionar Turno</option>
                                    <option value="AM" ${this.formData.appointment.shift === 'AM' ? 'selected' : ''}>AM</option>
                                    <option value="PM" ${this.formData.appointment.shift === 'PM' ? 'selected' : ''}>PM</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="professional">PROFESIONAL:</label>
                                <select class="form-control form-select" id="professional" name="professional" required>
                                    <option value="">Seleccionar Profesional</option>
                                    ${this.getActiveProfessionals().map(prof => `
                                        <option value="${prof.id}" ${this.formData.appointment.professional_id == prof.id ? 'selected' : ''}>
                                            ${prof.name}${this.getProfessionalStatus(prof)}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">FECHA:</label>
                                <div class="form-control bg-gray-100">
                                    ${this.formatDateDisplay(this.formData.appointment.date)}
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancel-appointment-btn">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-primary" id="save-appointment-btn">
                            Agendar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-appointment-btn').addEventListener('click', () => {
            this.closeModal('appointment');
        });
        
        document.getElementById('save-appointment-btn').addEventListener('click', () => {
            this.saveAppointment();
        });
        
        // Form input change events
        document.getElementById('box').addEventListener('change', (e) => {
            this.formData.appointment.box_id = e.target.value;
        });
        
        document.getElementById('shift').addEventListener('change', (e) => {
            this.formData.appointment.shift = e.target.value;
        });
        
        document.getElementById('professional').addEventListener('change', (e) => {
            this.formData.appointment.professional_id = e.target.value;
        });
    }
    
    /**
     * Render conflict modal
     */
    renderConflictModal() {
        const modalContent = `
            <div class="modal" id="conflict-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-br from-red-500 to-pink-600">
                        <h2 class="modal-title">Conflicto de Asignación</h2>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <p class="text-lg font-semibold mb-2">
                                El profesional ya se encuentra asignado en este turno
                            </p>
                            <div class="bg-red-50 rounded-lg p-4 border border-red-200">
                                <p class="mb-2">
                                    <span class="font-bold">Profesional:</span> ${this.formData.conflict.professional}
                                </p>
                                <p class="mb-2">
                                    <span class="font-bold">Box:</span> ${this.formData.conflict.box}
                                </p>
                                <p>
                                    <span class="font-bold">Turno:</span> ${this.formData.conflict.shift}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" id="conflict-understood-btn">
                            Entendido
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listener
        document.getElementById('conflict-understood-btn').addEventListener('click', () => {
            this.closeModal('conflict');
        });
    }
    
    /**
     * Render box add modal
     */
    renderBoxAddModal() {
        const modalContent = `
            <div class="modal" id="box-add-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-green-600 to-emerald-600">
                        <h2 class="modal-title">Agregar Nuevo Box</h2>
                    </div>
                    <div class="modal-body">
                        <form id="box-add-form">
                            <div class="form-group">
                                <label class="form-label" for="box-name">NOMBRE:</label>
                                <input type="text" class="form-control" id="box-name" name="box-name" 
                                       placeholder="Ingrese nombre del box" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancel-box-add-btn">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-success" id="save-box-add-btn">
                            Agregar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-box-add-btn').addEventListener('click', () => {
            this.closeModal('boxAdd');
        });
        
        document.getElementById('save-box-add-btn').addEventListener('click', () => {
            this.saveBox();
        });
        
        // Form input change events
        document.getElementById('box-name').addEventListener('input', (e) => {
            this.formData.box.name = e.target.value;
        });
    }
    
    /**
     * Render box delete modal
     */
    renderBoxDeleteModal() {
        const modalContent = `
            <div class="modal" id="box-delete-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-red-600 to-rose-600">
                        <h2 class="modal-title">Eliminar Box</h2>
                    </div>
                    <div class="modal-body">
                        <form id="box-delete-form">
                            <div class="form-group">
                                <label class="form-label" for="box-to-delete">SELECCIONAR:</label>
                                <select class="form-control form-select" id="box-to-delete" name="box-to-delete" required>
                                    <option value="">Seleccionar Box a eliminar</option>
                                    ${this.boxes.map(box => `
                                        <option value="${box.id}">${box.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <p class="text-sm text-danger text-center mt-4">
                                ⚠️ Esta acción eliminará el box y todas las citas futuras asociadas
                            </p>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancel-box-delete-btn">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-danger" id="confirm-box-delete-btn" disabled>
                            Eliminar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-box-delete-btn').addEventListener('click', () => {
            this.closeModal('boxDelete');
        });
        
        document.getElementById('confirm-box-delete-btn').addEventListener('click', () => {
            this.deleteBox();
        });
        
        // Form input change events
        document.getElementById('box-to-delete').addEventListener('change', (e) => {
            this.formData.box.id = e.target.value;
            document.getElementById('confirm-box-delete-btn').disabled = !e.target.value;
        });
    }
    
    /**
     * Render box edit modal
     */
    renderBoxEditModal() {
        const modalContent = `
            <div class="modal" id="box-edit-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-blue-600 to-indigo-600">
                        <h2 class="modal-title">Editar Box</h2>
                    </div>
                    <div class="modal-body">
                        <form id="box-edit-form">
                            <div class="form-group">
                                <label class="form-label" for="box-to-edit">BOX ACTUAL:</label>
                                <select class="form-control form-select" id="box-to-edit" name="box-to-edit" required>
                                    <option value="">Seleccionar Box a editar</option>
                                    ${this.boxes.map(box => `
                                        <option value="${box.id}">${box.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="new-box-name">NUEVO NOMBRE:</label>
                                <input type="text" class="form-control" id="new-box-name" name="new-box-name" 
                                       placeholder="Ingrese nuevo nombre" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancel-box-edit-btn">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-primary" id="save-box-edit-btn" disabled>
                            Modificar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-box-edit-btn').addEventListener('click', () => {
            this.closeModal('boxEdit');
        });
        
        document.getElementById('save-box-edit-btn').addEventListener('click', () => {
            this.updateBox();
        });
        
        // Form input change events
        document.getElementById('box-to-edit').addEventListener('change', (e) => {
            this.formData.box.id = e.target.value;
            
            if (e.target.value) {
                const selectedBox = this.boxes.find(box => box.id == e.target.value);
                if (selectedBox) {
                    this.formData.box.name = selectedBox.name;
                    document.getElementById('new-box-name').value = selectedBox.name;
                }
            }
            
            this.validateBoxEditForm();
        });
        
        document.getElementById('new-box-name').addEventListener('input', (e) => {
            this.formData.box.name = e.target.value;
            this.validateBoxEditForm();
        });
    }
    
    /**
     * Render professional add modal
     */
    renderProfessionalAddModal() {
        const modalContent = `
            <div class="modal" id="professional-add-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-teal-600 to-cyan-600">
                        <h2 class="modal-title">Agregar Nuevo Profesional</h2>
                    </div>
                    <div class="modal-body">
                        <form id="professional-add-form">
                            <div class="form-group">
                                <label class="form-label" for="professional-name">NOMBRE:</label>
                                <input type="text" class="form-control" id="professional-name" name="professional-name" 
                                       placeholder="Ingrese nombre del profesional" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancel-professional-add-btn">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-success" id="save-professional-add-btn">
                            Agregar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-professional-add-btn').addEventListener('click', () => {
            this.closeModal('professionalAdd');
        });
        
        document.getElementById('save-professional-add-btn').addEventListener('click', () => {
            this.saveProfessional();
        });
        
        // Form input change events
        document.getElementById('professional-name').addEventListener('input', (e) => {
            this.formData.professional.name = e.target.value;
        });
    }
    
    /**
     * Render professional delete modal
     */
    renderProfessionalDeleteModal() {
        const modalContent = `
            <div class="modal" id="professional-delete-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-red-600 to-rose-600">
                        <h2 class="modal-title">Eliminar Profesional</h2>
                    </div>
                    <div class="modal-body">
                        <form id="professional-delete-form">
                            <div class="form-group">
                                <label class="form-label" for="professional-to-delete">PROFESIONAL:</label>
                                <select class="form-control form-select" id="professional-to-delete" name="professional-to-delete" required>
                                    <option value="">Seleccionar profesional a eliminar</option>
                                    ${this.professionals.map(prof => `
                                        <option value="${prof.id}">${prof.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">OPCIÓN:</label>
                                <div>
                                    <div class="form-check">
                                        <input type="radio" id="delete-forever" name="delete-option" value="forever" checked
                                               class="form-check-input">
                                        <label for="delete-forever" class="form-check-label">Eliminar para siempre</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" id="delete-range" name="delete-option" value="range"
                                               class="form-check-input">
                                        <label for="delete-range" class="form-check-label">Eliminar en rango de fechas</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="date-range-container" class="hidden">
                                <div class="form-group">
                                    <label class="form-label" for="delete-date-from">DESDE:</label>
                                    <input type="date" class="form-control" id="delete-date-from" name="delete-date-from">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="delete-date-to">HASTA:</label>
                                    <input type="date" class="form-control" id="delete-date-to" name="delete-date-to">
                                </div>
                            </div>
                            
                            <p class="text-sm text-danger text-center mt-4">
                                ⚠️ Esta acción eliminará los registros del profesional del calendario
                            </p>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancel-professional-delete-btn">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-danger" id="confirm-professional-delete-btn" disabled>
                            Eliminar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-professional-delete-btn').addEventListener('click', () => {
            this.closeModal('professionalDelete');
        });
        
        document.getElementById('confirm-professional-delete-btn').addEventListener('click', () => {
            this.deleteProfessional();
        });
        
        // Form input change events
        document.getElementById('professional-to-delete').addEventListener('change', (e) => {
            this.formData.professional.id = e.target.value;
            this.validateProfessionalDeleteForm();
        });
        
        const deleteForeverRadio = document.getElementById('delete-forever');
        const deleteRangeRadio = document.getElementById('delete-range');
        const dateRangeContainer = document.getElementById('date-range-container');
        
        deleteForeverRadio.addEventListener('change', () => {
            if (deleteForeverRadio.checked) {
                dateRangeContainer.classList.add('hidden');
                this.formData.professional.deleteOption = 'forever';
                this.validateProfessionalDeleteForm();
            }
        });
        
        deleteRangeRadio.addEventListener('change', () => {
            if (deleteRangeRadio.checked) {
                dateRangeContainer.classList.remove('hidden');
                this.formData.professional.deleteOption = 'range';
                this.validateProfessionalDeleteForm();
            }
        });
        
        document.getElementById('delete-date-from').addEventListener('change', (e) => {
            this.formData.professional.status_from = e.target.value;
            this.validateProfessionalDeleteForm();
        });
        
        document.getElementById('delete-date-to').addEventListener('change', (e) => {
            this.formData.professional.status_to = e.target.value;
            this.validateProfessionalDeleteForm();
        });
    }
    
    /**
     * Render professional status edit modal
     */
    renderProfessionalStatusModal() {
        const modalContent = `
            <div class="modal" id="professional-status-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-amber-600 to-orange-600">
                        <h2 class="modal-title">Cambiar Estado de Profesional</h2>
                    </div>
                    <div class="modal-body">
                        <form id="professional-status-form">
                            <div class="form-group">
                                <label class="form-label" for="professional-for-status">PROFESIONAL:</label>
                                <select class="form-control form-select" id="professional-for-status" name="professional-for-status" required>
                                    <option value="">Seleccionar profesional</option>
                                    ${this.professionals.map(prof => `
                                        <option value="${prof.id}">${prof.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="professional-status">ESTADO:</label>
                                <select class="form-control form-select" id="professional-status" name="professional-status" required>
                                    <option value="Vigente">Vigente</option>
                                    <option value="Licencia médica">Licencia médica</option>
                                    <option value="Permiso administrativo">Permiso administrativo</option>
                                    <option value="Vacaciones">Vacaciones</option>
                                    <option value="Cursos">Cursos</option>
                                </select>
                            </div>
                            
                            <div id="status-date-container" class="hidden">
                                <div class="form-group">
                                    <label class="form-label" for="status-date-from">FECHA DESDE:</label>
                                    <input type="date" class="form-control" id="status-date-from" name="status-date-from">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="status-date-to">FECHA HASTA:</label>
                                    <input type="date" class="form-control" id="status-date-to" name="status-date-to">
                                </div>
                            </div>
                            
                            <p class="text-sm text-warning text-center mt-4">
                                ⚠️ Los agendamientos en las fechas indicadas serán eliminados del calendario
                            </p>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancel-status-btn">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-primary" id="save-status-btn" disabled>
                            Guardar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-status-btn').addEventListener('click', () => {
            this.closeModal('professionalStatus');
        });
        
        document.getElementById('save-status-btn').addEventListener('click', () => {
            this.updateProfessionalStatus();
        });
        
        // Form input change events
        document.getElementById('professional-for-status').addEventListener('change', (e) => {
            this.formData.professional.id = e.target.value;
            
            if (e.target.value) {
                const selectedProfessional = this.professionals.find(prof => prof.id == e.target.value);
                if (selectedProfessional) {
                    this.formData.professional.status = selectedProfessional.status || 'Vigente';
                    document.getElementById('professional-status').value = selectedProfessional.status || 'Vigente';
                    
                    if (selectedProfessional.status !== 'Vigente') {
                        document.getElementById('status-date-from').value = selectedProfessional.status_from || '';
                        document.getElementById('status-date-to').value = selectedProfessional.status_to || '';
                        this.formData.professional.status_from = selectedProfessional.status_from || '';
                        this.formData.professional.status_to = selectedProfessional.status_to || '';
                        document.getElementById('status-date-container').classList.remove('hidden');
                    } else {
                        document.getElementById('status-date-container').classList.add('hidden');
                    }
                }
            }
            
            this.validateProfessionalStatusForm();
        });
        
        document.getElementById('professional-status').addEventListener('change', (e) => {
            this.formData.professional.status = e.target.value;
            
            if (e.target.value !== 'Vigente') {
                document.getElementById('status-date-container').classList.remove('hidden');
            } else {
                document.getElementById('status-date-container').classList.add('hidden');
                this.formData.professional.status_from = '';
                this.formData.professional.status_to = '';
            }
            
            this.validateProfessionalStatusForm();
        });
        
        document.getElementById('status-date-from').addEventListener('change', (e) => {
            this.formData.professional.status_from = e.target.value;
            this.validateProfessionalStatusForm();
        });
        
        document.getElementById('status-date-to').addEventListener('change', (e) => {
            this.formData.professional.status_to = e.target.value;
            this.validateProfessionalStatusForm();
        });
    }
    
    /**
     * Render share options modal
     */
    renderShareOptionsModal() {
        const modalContent = `
            <div class="modal" id="share-options-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-blue-600 to-indigo-600">
                        <h2 class="modal-title">Compartir Agenda</h2>
                    </div>
                    <div class="modal-body">
                        <p class="text-center mb-4">
                            Selecciona cómo deseas compartir la agenda de este día:
                        </p>
                        
                        <div class="share-options">
                            <button class="share-option share-option-whatsapp" id="share-whatsapp-btn">
                                <span class="share-option-icon">📱</span>
                                <span>WhatsApp</span>
                            </button>
                            
                            <button class="share-option share-option-email" id="share-email-btn">
                                <span class="share-option-icon">📧</span>
                                <span>Correo</span>
                            </button>
                        </div>
                        
                        <div class="flex justify-center mt-4">
                            <button type="button" class="btn btn-secondary" id="cancel-share-options-btn">
                                Cancelar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('cancel-share-options-btn').addEventListener('click', () => {
            this.closeModal('shareOptions');
        });
        
        document.getElementById('share-whatsapp-btn').addEventListener('click', () => {
            this.formData.share.method = 'whatsapp';
            this.closeModal('shareOptions');
            this.getShareContent(this.formData.share.date);
        });
        
        document.getElementById('share-email-btn').addEventListener('click', () => {
            this.formData.share.method = 'email';
            this.closeModal('shareOptions');
            this.getShareContent(this.formData.share.date);
        });
    }
    
    /**
     * Render share preview modal
     */
    renderSharePreviewModal() {
        const dateObj = new Date(this.formData.share.date);
        const formattedDate = this.formatDateDisplay(this.formData.share.date);
        
        const modalContent = `
            <div class="modal" id="share-preview-modal">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-to-r from-purple-600 to-pink-600">
                        <h2 class="modal-title">Vista Previa para Compartir</h2>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <p class="text-lg font-semibold">${formattedDate}</p>
                        </div>
                        
                        <div class="share-preview">
                            ${this.formData.share.content}
                        </div>
                        
                        <div class="text-sm text-gray-500 text-center mb-4">
                            <p>
                                ${this.formData.share.method === 'whatsapp' 
                                    ? 'Se abrirá WhatsApp con este contenido preformateado' 
                                    : 'Se abrirá tu cliente de correo con este contenido'}
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="back-share-btn">
                            Volver
                        </button>
                        <button type="button" class="btn btn-primary" id="execute-share-btn">
                            ${this.formData.share.method === 'whatsapp' ? 'Abrir WhatsApp' : 'Abrir Correo'}
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalContent);
        
        // Add event listeners
        document.getElementById('back-share-btn').addEventListener('click', () => {
            this.closeModal('sharePreview');
            this.openModal('shareOptions');
        });
        
        document.getElementById('execute-share-btn').addEventListener('click', () => {
            this.executeShare();
        });
    }
    
    /**
     * Add event listeners to the page
     */
    addEventListeners() {
        // Month navigation
        document.getElementById('prev-month-btn').addEventListener('click', () => {
            this.changeMonth('prev');
        });
        
        document.getElementById('next-month-btn').addEventListener('click', () => {
            this.changeMonth('next');
        });
        
        // Box options menu
        const boxOptionsBtn = document.getElementById('box-options-btn');
        const boxOptionsMenu = document.getElementById('box-options-menu');
        
        boxOptionsBtn.addEventListener('click', () => {
            boxOptionsMenu.classList.toggle('hidden');
        });
        
        // Professional options menu
        const professionalOptionsBtn = document.getElementById('professional-options-btn');
        const professionalOptionsMenu = document.getElementById('professional-options-menu');
        
        professionalOptionsBtn.addEventListener('click', () => {
            professionalOptionsMenu.classList.toggle('hidden');
        });
        
        // Close menus when clicking outside
        document.addEventListener('click', (e) => {
            if (!boxOptionsBtn.contains(e.target) && !boxOptionsMenu.contains(e.target)) {
                boxOptionsMenu.classList.add('hidden');
            }
            
            if (!professionalOptionsBtn.contains(e.target) && !professionalOptionsMenu.contains(e.target)) {
                professionalOptionsMenu.classList.add('hidden');
            }
        });
        
        // Box management buttons
        document.getElementById('add-box-btn').addEventListener('click', () => {
            boxOptionsMenu.classList.add('hidden');
            this.openModal('boxAdd');
        });
        
        document.getElementById('delete-box-btn').addEventListener('click', () => {
            boxOptionsMenu.classList.add('hidden');
            this.openModal('boxDelete');
        });
        
        document.getElementById('edit-box-btn').addEventListener('click', () => {
            boxOptionsMenu.classList.add('hidden');
            this.openModal('boxEdit');
        });
        
        // Professional management buttons
        document.getElementById('add-professional-btn').addEventListener('click', () => {
            professionalOptionsMenu.classList.add('hidden');
            this.openModal('professionalAdd');
        });
        
        document.getElementById('delete-professional-btn').addEventListener('click', () => {
            professionalOptionsMenu.classList.add('hidden');
            this.openModal('professionalDelete');
        });
        
        document.getElementById('edit-status-btn').addEventListener('click', () => {
            professionalOptionsMenu.classList.add('hidden');
            this.openModal('professionalStatus');
        });
        
        // Calendar day click events
        const calendarDays = document.querySelectorAll('[data-date]');
        calendarDays.forEach(day => {
            day.addEventListener('click', () => {
                const dateStr = day.getAttribute('data-date');
                if (dateStr) {
                    this.handleDateClick(dateStr);
                }
            });
        });
        
        // Share buttons
        const shareBtns = document.querySelectorAll('.share-day-btn');
        shareBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent calendar day click
                const dateStr = btn.getAttribute('data-date');
                if (dateStr) {
                    this.handleShareClick(dateStr);
                }
            });
        });
        
        // Mobile calendar navigation
        if (this.isMobile) {
            const mobileCalendar = document.getElementById('mobile-calendar');
            const mobileCalendarContainer = document.getElementById('mobile-calendar-container');
            
            // Touch events for mobile swiping
            mobileCalendar.addEventListener('touchstart', (e) => {
                this.mobileCalendar.touchStartX = e.touches[0].clientX;
                this.mobileCalendar.isScrolling = true;
            });
            
            mobileCalendar.addEventListener('touchmove', (e) => {
                if (!this.mobileCalendar.isScrolling) return;
                this.mobileCalendar.touchEndX = e.touches[0].clientX;
            });
            
            mobileCalendar.addEventListener('touchend', () => {
                if (!this.mobileCalendar.isScrolling) return;
                
                const diff = this.mobileCalendar.touchStartX - this.mobileCalendar.touchEndX;
                const threshold = 50; // Minimum swipe distance
                const columnWidth = window.innerWidth * 0.42; // Width of each column
                
                if (Math.abs(diff) > threshold) {
                    if (diff > 0 && this.mobileCalendar.scrollPosition < this.mobileCalendar.maxScroll) {
                        // Swipe left - show more days
                        this.mobileCalendar.scrollPosition = Math.min(
                            this.mobileCalendar.scrollPosition + columnWidth, 
                            this.mobileCalendar.maxScroll
                        );
                    } else if (diff < 0 && this.mobileCalendar.scrollPosition > 0) {
                        // Swipe right - show previous days
                        this.mobileCalendar.scrollPosition = Math.max(
                            this.mobileCalendar.scrollPosition - columnWidth, 
                            0
                        );
                    }
                    
                    mobileCalendarContainer.style.transform = `translateX(-${this.mobileCalendar.scrollPosition}px)`;
                }
                
                this.mobileCalendar.isScrolling = false;
            });
            
            // Button navigation
            document.getElementById('mobile-prev-btn').addEventListener('click', () => {
                const columnWidth = window.innerWidth * 0.42;
                this.mobileCalendar.scrollPosition = Math.max(
                    this.mobileCalendar.scrollPosition - columnWidth, 
                    0
                );
                mobileCalendarContainer.style.transform = `translateX(-${this.mobileCalendar.scrollPosition}px)`;
            });
            
            document.getElementById('mobile-next-btn').addEventListener('click', () => {
                const columnWidth = window.innerWidth * 0.42;
                this.mobileCalendar.scrollPosition = Math.min(
                    this.mobileCalendar.scrollPosition + columnWidth, 
                    this.mobileCalendar.maxScroll
                );
                mobileCalendarContainer.style.transform = `translateX(-${this.mobileCalendar.scrollPosition}px)`;
            });
            
            // Calculate maximum scroll
            this.updateMobileMaxScroll();
        }
    }
    
    /**
     * Open a modal
     * 
     * @param {string} modalType - Type of modal to open
     */
    openModal(modalType) {
        // Reset form data based on modal type
        this.resetFormData(modalType);
        
        // Set modal state to open
        this.modalStates[modalType] = true;
        
        // Render the modal
        switch (modalType) {
            case 'appointment':
                this.renderAppointmentModal();
                break;
            case 'conflict':
                this.renderConflictModal();
                break;
            case 'boxAdd':
                this.renderBoxAddModal();
                break;
            case 'boxDelete':
                this.renderBoxDeleteModal();
                break;
            case 'boxEdit':
                this.renderBoxEditModal();
                break;
            case 'professionalAdd':
                this.renderProfessionalAddModal();
                break;
            case 'professionalDelete':
                this.renderProfessionalDeleteModal();
                break;
            case 'professionalStatus':
                this.renderProfessionalStatusModal();
                break;
            case 'shareOptions':
                this.renderShareOptionsModal();
                break;
            case 'sharePreview':
                this.renderSharePreviewModal();
                break;
        }
    }
    
    /**
     * Close a modal
     * 
     * @param {string} modalType - Type of modal to close
     */
    closeModal(modalType) {
        // Set modal state to closed
        this.modalStates[modalType] = false;
        
        // Remove modal element
        const modalId = `${modalType}-modal`.replace(/([A-Z])/g, '-$1').toLowerCase();
        const modalElement = document.getElementById(modalId);
        
        if (modalElement) {
            modalElement.remove();
        }
    }
    
    /**
     * Reset form data based on modal type
     * 
     * @param {string} modalType - Type of modal
     */
    resetFormData(modalType) {
        switch (modalType) {
            case 'appointment':
                // Keep date from caller, reset other fields
                const currentDate = this.formData.appointment.date;
                this.formData.appointment = {
                    id: null,
                    date: currentDate,
                    box_id: '',
                    shift: '',
                    professional_id: ''
                };
                break;
            
            case 'boxAdd':
                this.formData.box = {
                    id: null,
                    name: ''
                };
                break;
            
            case 'boxDelete':
                this.formData.box = {
                    id: null,
                    name: ''
                };
                break;
            
            case 'boxEdit':
                this.formData.box = {
                    id: null,
                    name: ''
                };
                break;
            
            case 'professionalAdd':
                this.formData.professional = {
                    id: null,
                    name: '',
                    status: 'Vigente',
                    status_from: '',
                    status_to: ''
                };
                break;
            
            case 'professionalDelete':
                this.formData.professional = {
                    id: null,
                    name: '',
                    status: 'Vigente',
                    status_from: '',
                    status_to: '',
                    deleteOption: 'forever'
                };
                break;
            
            case 'professionalStatus':
                this.formData.professional = {
                    id: null,
                    name: '',
                    status: 'Vigente',
                    status_from: '',
                    status_to: ''
                };
                break;
            
            // Note: shareOptions and sharePreview don't reset data as they share the same data
        }
    }
    
    /**
     * Handle date click in calendar
     * 
     * @param {string} dateStr - Date string (YYYY-MM-DD)
     */
    handleDateClick(dateStr) {
        this.formData.appointment.date = dateStr;
        this.openModal('appointment');
    }
    
    /**
     * Handle share button click
     * 
     * @param {string} dateStr - Date string (YYYY-MM-DD)
     */
    handleShareClick(dateStr) {
        this.formData.share.date = dateStr;
        this.openModal('shareOptions');
    }
    
    /**
     * Change the calendar month
     * 
     * @param {string} direction - Direction to change (prev/next)
     */
    async changeMonth(direction) {
        const newDate = new Date(this.currentDate);
        
        if (direction === 'prev') {
            newDate.setMonth(newDate.getMonth() - 1);
        } else {
            newDate.setMonth(newDate.getMonth() + 1);
        }
        
        this.currentDate = newDate;
        
        try {
            // Fetch appointments for the new month
            await this.fetchAppointmentsByMonth(
                this.currentDate.getFullYear(),
                this.currentDate.getMonth() + 1
            );
            
            // Re-render the application
            this.render();
        } catch (error) {
            console.error('Error changing month:', error);
            // Roll back to previous month on error
            if (direction === 'prev') {
                this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            } else {
                this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            }
        }
    }
    
    /**
     * Save appointment
     */
    async saveAppointment() {
        try {
            const { date, box_id, shift, professional_id } = this.formData.appointment;
            
            // Validate form data
            if (!date || !box_id || !shift || !professional_id) {
                alert('Por favor, complete todos los campos.');
                return;
            }
            
            // Check if the professional is assigned to another box in the same shift
            const professionalName = this.professionals.find(p => p.id == professional_id)?.name;
            const existingAppointment = this.appointments.find(a => 
                a.professional_id == professional_id && 
                a.appointment_date === date && 
                a.shift === shift
            );
            
            if (existingAppointment) {
                const boxName = this.boxes.find(b => b.id == existingAppointment.box_id)?.name;
                
                // Set conflict information
                this.formData.conflict = {
                    professional: professionalName,
                    box: boxName,
                    shift: existingAppointment.shift
                };
                
                // Close appointment modal and open conflict modal
                this.closeModal('appointment');
                this.openModal('conflict');
                return;
            }
            
            // API request data
            const requestData = {
                date,
                box_id: parseInt(box_id),
                shift,
                professional_id: parseInt(professional_id)
            };
            
            // API request
            const response = await fetch(`${API_URL}/appointments.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Add new appointment to the local data
                this.appointments.push(result.data);
                
                // Close modal
                this.closeModal('appointment');
                
                // Re-render the application
                this.render();
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error saving appointment:', error);
            alert('Error al guardar la cita. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Save box
     */
    async saveBox() {
        try {
            const { name } = this.formData.box;
            
            // Validate form data
            if (!name.trim()) {
                alert('Por favor, ingrese un nombre para el box.');
                return;
            }
            
            // Check if box name already exists
            if (this.boxes.some(box => box.name.toLowerCase() === name.trim().toLowerCase())) {
                alert('Ya existe un box con ese nombre.');
                return;
            }
            
            // API request data
            const requestData = { name: name.trim() };
            
            // API request
            const response = await fetch(`${API_URL}/boxes.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Add new box to the local data
                this.boxes.push(result.data);
                
                // Close modal
                this.closeModal('boxAdd');
                
                // Re-render the application
                this.render();
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error saving box:', error);
            alert('Error al guardar el box. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Delete box
     */
    async deleteBox() {
        try {
            const { id } = this.formData.box;
            
            // Validate form data
            if (!id) {
                alert('Por favor, seleccione un box para eliminar.');
                return;
            }
            
            // API request data
            const requestData = { id: parseInt(id) };
            
            // API request
            const response = await fetch(`${API_URL}/boxes.php`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Remove box from local data
                this.boxes = this.boxes.filter(box => box.id != id);
                
                // Remove appointments for this box
                this.appointments = this.appointments.filter(appointment => appointment.box_id != id);
                
                // Close modal
                this.closeModal('boxDelete');
                
                // Re-render the application
                this.render();
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error deleting box:', error);
            alert('Error al eliminar el box. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Update box
     */
    async updateBox() {
        try {
            const { id, name } = this.formData.box;
            
            // Validate form data
            if (!id || !name.trim()) {
                alert('Por favor, complete todos los campos.');
                return;
            }
            
            // Check if new name already exists (excluding current box)
            const currentBox = this.boxes.find(box => box.id == id);
            if (currentBox.name !== name.trim() && 
                this.boxes.some(box => box.id != id && box.name.toLowerCase() === name.trim().toLowerCase())) {
                alert('Ya existe un box con ese nombre.');
                return;
            }
            
            // API request data
            const requestData = { 
                id: parseInt(id),
                name: name.trim()
            };
            
            // API request
            const response = await fetch(`${API_URL}/boxes.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Update box in local data
                this.boxes = this.boxes.map(box => box.id == id ? result.data : box);
                
                // Close modal
                this.closeModal('boxEdit');
                
                // Re-render the application
                this.render();
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error updating box:', error);
            alert('Error al actualizar el box. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Save professional
     */
    async saveProfessional() {
        try {
            const { name } = this.formData.professional;
            
            // Validate form data
            if (!name.trim()) {
                alert('Por favor, ingrese un nombre para el profesional.');
                return;
            }
            
            // Check if professional name already exists
            if (this.professionals.some(prof => prof.name.toLowerCase() === name.trim().toLowerCase())) {
                alert('Ya existe un profesional con ese nombre.');
                return;
            }
            
            // API request data
            const requestData = { name: name.trim() };
            
            // API request
            const response = await fetch(`${API_URL}/professionals.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Add new professional to the local data
                this.professionals.push(result.data);
                
                // Close modal
                this.closeModal('professionalAdd');
                
                // Re-render the application
                this.render();
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error saving professional:', error);
            alert('Error al guardar el profesional. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Delete professional
     */
    async deleteProfessional() {
        try {
            const { id, deleteOption, status_from, status_to } = this.formData.professional;
            
            // Validate form data
            if (!id) {
                alert('Por favor, seleccione un profesional para eliminar.');
                return;
            }
            
            if (deleteOption === 'range' && (!status_from || !status_to)) {
                alert('Por favor, ingrese el rango de fechas para eliminar.');
                return;
            }
            
            // API request data
            const requestData = { id: parseInt(id) };
            
            // Add date range if selected
            if (deleteOption === 'range') {
                requestData.date_range = {
                    from: status_from,
                    to: status_to
                };
            }
            
            // API request
            const response = await fetch(`${API_URL}/professionals.php`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                if (deleteOption === 'forever') {
                    // Remove professional from local data
                    this.professionals = this.professionals.filter(prof => prof.id != id);
                    
                    // Remove appointments for this professional
                    this.appointments = this.appointments.filter(appointment => 
                        appointment.professional_id != id
                    );
                } else {
                    // Remove appointments for this professional in the specified date range
                    const fromDate = new Date(status_from);
                    const toDate = new Date(status_to);
                    
                    this.appointments = this.appointments.filter(appointment => {
                        const appointmentDate = new Date(appointment.appointment_date);
                        return appointment.professional_id != id || 
                               appointmentDate < fromDate || 
                               appointmentDate > toDate;
                    });
                }
                
                // Close modal
                this.closeModal('professionalDelete');
                
                // Re-render the application
                this.render();
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error deleting professional:', error);
            alert('Error al eliminar el profesional. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Update professional status
     */
    async updateProfessionalStatus() {
        try {
            const { id, status, status_from, status_to } = this.formData.professional;
            
            // Validate form data
            if (!id || !status) {
                alert('Por favor, complete todos los campos.');
                return;
            }
            
            if (status !== 'Vigente' && (!status_from || !status_to)) {
                alert('Por favor, ingrese las fechas para el estado.');
                return;
            }
            
            // API request data
            const requestData = { 
                id: parseInt(id),
                status_update: true,
                status
            };
            
            if (status !== 'Vigente') {
                requestData.status_from = status_from;
                requestData.status_to = status_to;
            }
            
            // API request
            const response = await fetch(`${API_URL}/professionals.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Update professional in local data
                this.professionals = this.professionals.map(prof => prof.id == id ? result.data : prof);
                
                // If status is not 'Vigente', remove appointments in the date range
                if (status !== 'Vigente' && status_from && status_to) {
                    const fromDate = new Date(status_from);
                    const toDate = new Date(status_to);
                    
                    this.appointments = this.appointments.filter(appointment => {
                        const appointmentDate = new Date(appointment.appointment_date);
                        return appointment.professional_id != id || 
                               appointmentDate < fromDate || 
                               appointmentDate > toDate;
                    });
                }
                
                // Close modal
                this.closeModal('professionalStatus');
                
                // Re-render the application
                this.render();
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error updating professional status:', error);
            alert('Error al actualizar el estado del profesional. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Get share content from API
     * 
     * @param {string} dateStr - Date string (YYYY-MM-DD)
     */
    async getShareContent(dateStr) {
        try {
            // API request
            const response = await fetch(`${API_URL}/share.php?date=${dateStr}`);
            const result = await response.json();
            
            if (result.success) {
                // Set share content
                this.formData.share.content = result.data.share_text;
                
                // Open share preview modal
                this.openModal('sharePreview');
            } else {
                alert(`Error: ${result.message}`);
            }
        } catch (error) {
            console.error('Error getting share content:', error);
            alert('Error al obtener el contenido para compartir. Por favor, intente nuevamente.');
        }
    }
    
    /**
     * Execute share action
     */
    executeShare() {
        const { date, method, content } = this.formData.share;
        
        if (!date || !content) {
            alert('No hay contenido para compartir.');
            return;
        }
        
        // Encode content for sharing
        const encodedContent = encodeURIComponent(content);
        
        if (method === 'whatsapp') {
            // Open WhatsApp with pre-formatted content
            const whatsappUrl = `https://wa.me/?text=${encodedContent}`;
            window.open(whatsappUrl, '_blank');
        } else if (method === 'email') {
            // Format date for email subject
            const dateObj = new Date(date);
            const formattedDate = dateObj.toLocaleDateString('es-ES', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            // Open email client with pre-formatted content
            const subject = encodeURIComponent(`Agenda del ${formattedDate}`);
            const emailUrl = `mailto:?subject=${subject}&body=${encodedContent}`;
            window.location.href = emailUrl;
        }
        
        // Close modal
        this.closeModal('sharePreview');
    }
    
    /**
     * Validate box edit form
     */
    validateBoxEditForm() {
        const saveBtn = document.getElementById('save-box-edit-btn');
        const { id, name } = this.formData.box;
        
        // Get original box name for comparison
        const originalBox = this.boxes.find(box => box.id == id);
        const originalName = originalBox ? originalBox.name : '';
        
        // Button is disabled if:
        // - No box selected
        // - No name entered
        // - Name is the same as original
        // - Name already exists (case insensitive)
        const nameExists = this.boxes.some(box => 
            box.id != id && 
            box.name.toLowerCase() === name.trim().toLowerCase()
        );
        
        saveBtn.disabled = !id || 
                           !name.trim() || 
                           name.trim() === originalName || 
                           nameExists;
    }
    
    /**
     * Validate professional delete form
     */
    validateProfessionalDeleteForm() {
        const deleteBtn = document.getElementById('confirm-professional-delete-btn');
        const { id, deleteOption, status_from, status_to } = this.formData.professional;
        
        // Button is disabled if:
        // - No professional selected
        // - Range option selected but dates not provided
        let isDisabled = !id;
        
        if (deleteOption === 'range') {
            isDisabled = isDisabled || !status_from || !status_to;
        }
        
        deleteBtn.disabled = isDisabled;
    }
    
    /**
     * Validate professional status form
     */
    validateProfessionalStatusForm() {
        const saveBtn = document.getElementById('save-status-btn');
        const { id, status, status_from, status_to } = this.formData.professional;
        
        // Button is disabled if:
        // - No professional selected
        // - No status selected
        // - Status is not 'Vigente' but dates not provided
        let isDisabled = !id || !status;
        
        if (status !== 'Vigente') {
            isDisabled = isDisabled || !status_from || !status_to;
        }
        
        saveBtn.disabled = isDisabled;
    }
    
    /**
     * Update mobile calendar maximum scroll
     */
    updateMobileMaxScroll() {
        if (!this.isMobile) return;
        
        const mobileCalendar = document.getElementById('mobile-calendar');
        if (!mobileCalendar) return;
        
        const scrollWidth = mobileCalendar.scrollWidth;
        const columnWidth = window.innerWidth * 0.42; // Width of each column
        const targetVisibleWidth = columnWidth * 2.5; // Show 2.5 columns
        
        this.mobileCalendar.maxScroll = Math.max(0, scrollWidth - targetVisibleWidth);
    }
    
    /**
     * Fetch boxes from API
     */
    async fetchBoxes() {
        try {
            const response = await fetch(`${API_URL}/boxes.php`);
            const result = await response.json();
            
            if (result.success) {
                this.boxes = result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error fetching boxes:', error);
            throw error;
        }
    }
    
    /**
     * Fetch professionals from API
     */
    async fetchProfessionals() {
        try {
            const response = await fetch(`${API_URL}/professionals.php`);
            const result = await response.json();
            
            if (result.success) {
                this.professionals = result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error fetching professionals:', error);
            throw error;
        }
    }
    
    /**
     * Fetch appointments by month from API
     * 
     * @param {number} year - Year
     * @param {number} month - Month (1-12)
     */
    async fetchAppointmentsByMonth(year, month) {
        try {
            const response = await fetch(`${API_URL}/appointments.php?year=${year}&month=${month}`);
            const result = await response.json();
            
            if (result.success) {
                this.appointments = result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error fetching appointments:', error);
            throw error;
        }
    }
    
    /**
     * Get days in month for calendar
     * 
     * @param {Date} date - Date object
     * @returns {Array} Array of day objects
     */
    getDaysInMonth(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();
        
        const days = [];
        
        // Adjust for Monday as first day of week (0 = Sunday, 1 = Monday)
        const adjustedStartDay = startingDayOfWeek === 0 ? 6 : startingDayOfWeek - 1;
        
        // Add empty cells for days before first day of month
        for (let i = 0; i < adjustedStartDay; i++) {
            days.push({ date: null, dayNumber: null });
        }
        
        // Add all days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayDate = new Date(year, month, day);
            days.push({ date: dayDate, dayNumber: day });
        }
        
        return days;
    }
    
    /**
     * Get weeks for mobile calendar
     * 
     * @returns {Array} Array of week arrays
     */
    getWeeksForMobile() {
        const days = this.getDaysInMonth(this.currentDate);
        const weeks = [];
        let currentWeek = [];
        
        days.forEach((dayData, index) => {
            if (!dayData.date || !dayData.dayNumber) {
                // Add empty day to current week
                currentWeek.push({ 
                    dayData, 
                    dayIndex: index, 
                    dayOfWeekIndex: -1 
                });
                return;
            }
            
            const dayOfWeek = dayData.date.getDay();
            const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
            
            // Skip weekends
            if (isWeekend) {
                return;
            }
            
            // Calculate column index (0-4 for Monday-Friday)
            const adjustedDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
            
            currentWeek.push({ 
                dayData, 
                dayIndex: index, 
                dayOfWeekIndex: adjustedDayOfWeek 
            });
            
            // If we have 5 days of the week (Monday-Friday), close the week
            if (currentWeek.length === 5) {
                weeks.push([...currentWeek]);
                currentWeek = [];
            }
        });
        
        // Add the last week if it has days
        if (currentWeek.length > 0) {
            weeks.push([...currentWeek]);
        }
        
        return weeks;
    }
    
    /**
     * Get appointments for a specific date
     * 
     * @param {Date} date - Date to check
     * @returns {Array} Array of appointments for the date
     */
    getAppointmentsForDate(date) {
        return this.appointments.filter(appointment => 
            appointment.appointment_date === this.formatDate(date)
        );
    }
    
    /**
     * Get active professionals (not on leave for current date)
     * 
     * @returns {Array} Array of active professionals
     */
    getActiveProfessionals() {
        const today = new Date();
        
        return this.professionals.filter(prof => {
            if (prof.status === 'Vigente') {
                return true;
            }
            
            if (prof.status_from && prof.status_to) {
                const fromDate = new Date(prof.status_from);
                const toDate = new Date(prof.status_to);
                
                return today < fromDate || today > toDate;
            }
            
            return false;
        });
    }
    
    /**
     * Get professional status display text
     * 
     * @param {Object} professional - Professional object
     * @returns {string} Status text if active, empty string otherwise
     */
    getProfessionalStatus(professional) {
        if (!professional || professional.status === 'Vigente') {
            return '';
        }
        
        const today = new Date();
        
        if (professional.status_from && professional.status_to) {
            const fromDate = new Date(professional.status_from);
            const toDate = new Date(professional.status_to);
            
            if (today >= fromDate && today <= toDate) {
                return ` (${professional.status})`;
            }
        }
        
        return '';
    }
    
    /**
     * Check if a date is today
     * 
     * @param {Date} date - Date to check
     * @returns {boolean} True if date is today, false otherwise
     */
    isToday(date) {
        const today = new Date();
        return date.getDate() === today.getDate() &&
               date.getMonth() === today.getMonth() &&
               date.getFullYear() === today.getFullYear();
    }
    
    /**
     * Format date to YYYY-MM-DD string
     * 
     * @param {Date} date - Date to format
     * @returns {string} Formatted date string
     */
    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    }
    
    /**
     * Format date for display
     * 
     * @param {string} dateStr - Date string (YYYY-MM-DD)
     * @returns {string} Formatted date for display
     */
    formatDateDisplay(dateStr) {
        if (!dateStr) return '';
        
        const date = new Date(dateStr);
        const day = date.getDate();
        const month = MONTHS[date.getMonth()];
        const year = date.getFullYear();
        const dayName = DAY_NAMES[date.getDay()];
        
        return `${dayName}, ${day} de ${month} de ${year}`;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    // Add Font Awesome
    const fontAwesome = document.createElement('link');
    fontAwesome.rel = 'stylesheet';
    fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
    document.head.appendChild(fontAwesome);
    
    // Start the app
    const app = new AgendaApp();
});