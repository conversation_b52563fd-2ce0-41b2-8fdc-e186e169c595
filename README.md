# Sistema de Calendario y Agendamiento

Un sistema completo para gestionar citas y horarios de profesionales médicos, desarrollado con PHP, MySQL, JavaScript, HTML y CSS.

## Características

- Calendario interactivo para visualizar citas programadas
- Gestión de boxes (consultorios)
- Gestión de profesionales y sus estados (vigente, licencia médica, etc.)
- Agendamiento de citas con validación de conflictos
- Diseño responsivo para dispositivos móviles y escritorio
- Funcionalidad para compartir información de citas por WhatsApp y correo electrónico

## Requisitos

- PHP 7.4 o superior
- MySQL 5.7 o superior
- Servidor web Apache con mod_rewrite habilitado

## Instalación

1. Clone el repositorio:
```
git clone https://github.com/your-username/agenda_Cotte.git
```

2. Configure la base de datos:
   - Cree una base de datos MySQL
   - Importe el archivo `database/schema.sql`
   - Actualice las credenciales de conexión en `database/db_connection.php`

3. Configure su servidor web:
   - Asegúrese de que Apache tenga habilitado mod_rewrite
   - Asegúrese de que la carpeta tenga los permisos adecuados

4. Acceda a la aplicación desde su navegador:
```
http://localhost/agenda_Cotte/
```

## Estructura del Proyecto

- `api/`: Endpoints de la API REST
- `assets/`: Archivos estáticos (CSS, JS, imágenes)
- `database/`: Esquema de base de datos y configuración
- `includes/`: Archivos de configuración y modelos
- `index.php`: Punto de entrada principal

## Uso

### Gestión de Boxes
- Agregar, editar y eliminar boxes

### Gestión de Profesionales
- Agregar profesionales
- Establecer estados (vigente, licencia médica, etc.)
- Eliminar profesionales

### Agendamiento
- Programar citas seleccionando fecha, box, turno y profesional
- Ver citas programadas en el calendario
- Compartir información de citas por WhatsApp o correo electrónico

## Licencia

[MIT](LICENSE)

## Autor

Agenda Cotte Team