<?php
/**
 * API endpoint for sharing appointment information
 */

require_once dirname(__DIR__) . '/includes/config.php';
require_once INCLUDES_PATH . '/models/Appointment.php';

// Set headers for API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Initialize Appointment model
$appointmentModel = new Appointment();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Only allow GET and POST methods
if ($method !== 'GET' && $method !== 'POST') {
    json_response(['success' => false, 'message' => 'Método no permitido'], 405);
}

// Get date parameter
$date = isset($_GET['date']) ? $_GET['date'] : null;

// Validate date
if (!$date || !validate_date($date)) {
    json_response(['success' => false, 'message' => 'Fecha requerida en formato YYYY-MM-DD'], 400);
}

// Get appointments for the date
$appointments = $appointmentModel->getAppointmentsByDate($date);

// Format date for display
$dateObj = new DateTime($date);
$dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
$monthNames = [
    'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
    'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
];

$dayName = $dayNames[$dateObj->format('w')];
$dayNumber = $dateObj->format('j');
$monthName = $monthNames[$dateObj->format('n') - 1];
$year = $dateObj->format('Y');

// Organize appointments by box and shift
$formattedAppointments = [];
foreach ($appointments as $appointment) {
    $boxName = $appointment['box_name'];
    $shift = $appointment['shift'];
    $professionalName = $appointment['professional_name'];
    
    if (!isset($formattedAppointments[$boxName])) {
        $formattedAppointments[$boxName] = [
            'AM' => null,
            'PM' => null
        ];
    }
    
    $formattedAppointments[$boxName][$shift] = $professionalName;
}

// Generate formatted output for sharing
$shareText = "📅 *Agenda del {$dayName}, {$dayNumber} de {$monthName} de {$year}*\n\n";

if (empty($formattedAppointments)) {
    $shareText .= "📌 *No hay agendamientos programados para este día.*\n\n";
} else {
    foreach ($formattedAppointments as $boxName => $shifts) {
        $shareText .= "🏥 *{$boxName}:*\n";
        
        if ($shifts['AM']) {
            $shareText .= "   🌅 *Turno AM:* {$shifts['AM']}\n";
        }
        
        if ($shifts['PM']) {
            $shareText .= "   🌆 *Turno PM:* {$shifts['PM']}\n";
        }
        
        $shareText .= "\n";
    }
}

$shareText .= "🔄 Generado desde el Sistema de Calendario y Agendamiento";

// Return formatted data
json_response([
    'success' => true,
    'data' => [
        'date' => [
            'raw' => $date,
            'formatted' => "{$dayName}, {$dayNumber} de {$monthName} de {$year}"
        ],
        'appointments' => $formattedAppointments,
        'share_text' => $shareText
    ]
]);