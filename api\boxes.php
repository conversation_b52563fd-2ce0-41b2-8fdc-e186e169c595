<?php
/**
 * API endpoints for Boxes
 */

require_once dirname(__DIR__) . '/includes/config.php';
require_once INCLUDES_PATH . '/models/Box.php';

// Set headers for API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Initialize Box model
$boxModel = new Box();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle requests based on method
switch ($method) {
    case 'GET':
        // Get all boxes
        $boxes = $boxModel->getAllBoxes();
        json_response(['success' => true, 'data' => $boxes]);
        break;
        
    case 'POST':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['name']) || empty(trim($data['name']))) {
            json_response(['success' => false, 'message' => 'El nombre del box es requerido'], 400);
        }
        
        $name = sanitize_data($data['name']);
        
        // Check if box name already exists
        if ($boxModel->boxNameExists($name)) {
            json_response(['success' => false, 'message' => 'El nombre del box ya existe'], 400);
        }
        
        // Add new box
        $boxId = $boxModel->addBox($name);
        
        if ($boxId) {
            $box = $boxModel->getBoxById($boxId);
            json_response(['success' => true, 'message' => 'Box agregado exitosamente', 'data' => $box]);
        } else {
            json_response(['success' => false, 'message' => 'Error al agregar el box'], 500);
        }
        break;
        
    case 'PUT':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            json_response(['success' => false, 'message' => 'ID de box inválido'], 400);
        }
        
        if (!isset($data['name']) || empty(trim($data['name']))) {
            json_response(['success' => false, 'message' => 'El nombre del box es requerido'], 400);
        }
        
        $id = (int) $data['id'];
        $name = sanitize_data($data['name']);
        
        // Check if box exists
        $box = $boxModel->getBoxById($id);
        if (!$box) {
            json_response(['success' => false, 'message' => 'Box no encontrado'], 404);
        }
        
        // Check if new name already exists
        if ($boxModel->boxNameExists($name, $id)) {
            json_response(['success' => false, 'message' => 'El nombre del box ya existe'], 400);
        }
        
        // Update box
        if ($boxModel->updateBox($id, $name)) {
            $updatedBox = $boxModel->getBoxById($id);
            json_response(['success' => true, 'message' => 'Box actualizado exitosamente', 'data' => $updatedBox]);
        } else {
            json_response(['success' => false, 'message' => 'Error al actualizar el box'], 500);
        }
        break;
        
    case 'DELETE':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            json_response(['success' => false, 'message' => 'ID de box inválido'], 400);
        }
        
        $id = (int) $data['id'];
        
        // Check if box exists
        $box = $boxModel->getBoxById($id);
        if (!$box) {
            json_response(['success' => false, 'message' => 'Box no encontrado'], 404);
        }
        
        // Delete box
        if ($boxModel->deleteBox($id)) {
            json_response(['success' => true, 'message' => 'Box eliminado exitosamente']);
        } else {
            json_response(['success' => false, 'message' => 'Error al eliminar el box'], 500);
        }
        break;
        
    default:
        json_response(['success' => false, 'message' => 'Método no permitido'], 405);
}