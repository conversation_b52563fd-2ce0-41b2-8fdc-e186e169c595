<?php
/**
 * Box Model for Agenda Cotte
 */

class Box {
    private $conn;
    
    /**
     * Constructor - Initialize database connection
     */
    public function __construct() {
        $this->conn = get_db_connection();
    }
    
    /**
     * Destructor - Close database connection
     */
    public function __destruct() {
        close_db_connection($this->conn);
    }
    
    /**
     * Get all boxes
     * 
     * @return array List of all boxes
     */
    public function getAllBoxes() {
        $boxes = [];
        
        $sql = "SELECT * FROM boxes ORDER BY name";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $boxes[] = $row;
            }
        }
        
        return $boxes;
    }
    
    /**
     * Get box by ID
     * 
     * @param int $id Box ID
     * @return array|null Box data or null if not found
     */
    public function getBoxById($id) {
        $sql = "SELECT * FROM boxes WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    /**
     * Add new box
     * 
     * @param string $name Box name
     * @return int|bool New box ID or false on error
     */
    public function addBox($name) {
        $sql = "INSERT INTO boxes (name) VALUES (?)";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $name);
        
        if ($stmt->execute()) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update box
     * 
     * @param int $id Box ID
     * @param string $name New box name
     * @return bool True on success, false on error
     */
    public function updateBox($id, $name) {
        $sql = "UPDATE boxes SET name = ? WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("si", $name, $id);
        
        return $stmt->execute();
    }
    
    /**
     * Delete box
     * 
     * @param int $id Box ID
     * @return bool True on success, false on error
     */
    public function deleteBox($id) {
        $sql = "DELETE FROM boxes WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        
        return $stmt->execute();
    }
    
    /**
     * Check if box name exists
     * 
     * @param string $name Box name
     * @param int|null $excludeId Exclude box ID (for updates)
     * @return bool True if exists, false otherwise
     */
    public function boxNameExists($name, $excludeId = null) {
        if ($excludeId !== null) {
            $sql = "SELECT COUNT(*) AS count FROM boxes WHERE name = ? AND id != ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("si", $name, $excludeId);
        } else {
            $sql = "SELECT COUNT(*) AS count FROM boxes WHERE name = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("s", $name);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['count'] > 0;
    }
}