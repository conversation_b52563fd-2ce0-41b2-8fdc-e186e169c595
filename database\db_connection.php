<?php
/**
 * Database connection configuration for Agenda Cotte
 */

// Database configuration
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'agenda_cotte'
];

/**
 * Get database connection
 * 
 * @return mysqli|false Database connection or false on error
 */
function get_db_connection() {
    global $db_config;
    
    // Create connection
    $conn = new mysqli(
        $db_config['host'],
        $db_config['username'],
        $db_config['password'],
        $db_config['database']
    );
    
    // Check connection
    if ($conn->connect_error) {
        error_log("Connection failed: " . $conn->connect_error);
        return false;
    }
    
    // Set charset to utf8
    $conn->set_charset("utf8");
    
    return $conn;
}

/**
 * Close database connection
 * 
 * @param mysqli $conn Database connection
 * @return void
 */
function close_db_connection($conn) {
    if ($conn) {
        $conn->close();
    }
}