<?php
/**
 * API endpoints for Professionals
 */

require_once dirname(__DIR__) . '/includes/config.php';
require_once INCLUDES_PATH . '/models/Professional.php';

// Set headers for API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Initialize Professional model
$professionalModel = new Professional();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle requests based on method
switch ($method) {
    case 'GET':
        // Check for active param
        if (isset($_GET['active']) && isset($_GET['date'])) {
            // Validate date
            if (!validate_date($_GET['date'])) {
                json_response(['success' => false, 'message' => 'Formato de fecha inválido'], 400);
            }
            
            $date = $_GET['date'];
            
            // Get active professionals for date
            $professionals = $professionalModel->getActiveProfessionalsByDate($date);
        } else {
            // Get all professionals
            $professionals = $professionalModel->getAllProfessionals();
        }
        
        json_response(['success' => true, 'data' => $professionals]);
        break;
        
    case 'POST':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['name']) || empty(trim($data['name']))) {
            json_response(['success' => false, 'message' => 'El nombre del profesional es requerido'], 400);
        }
        
        $name = sanitize_data($data['name']);
        $status = isset($data['status']) ? sanitize_data($data['status']) : 'Vigente';
        $statusFrom = null;
        $statusTo = null;
        
        // Validate dates if status is not 'Vigente'
        if ($status !== 'Vigente') {
            if (isset($data['status_from']) && !empty($data['status_from'])) {
                if (!validate_date($data['status_from'])) {
                    json_response(['success' => false, 'message' => 'Formato de fecha inicial inválido'], 400);
                }
                $statusFrom = $data['status_from'];
            }
            
            if (isset($data['status_to']) && !empty($data['status_to'])) {
                if (!validate_date($data['status_to'])) {
                    json_response(['success' => false, 'message' => 'Formato de fecha final inválido'], 400);
                }
                $statusTo = $data['status_to'];
            }
            
            // Check date range
            if ($statusFrom && $statusTo && strtotime($statusFrom) > strtotime($statusTo)) {
                json_response(['success' => false, 'message' => 'La fecha inicial debe ser anterior a la fecha final'], 400);
            }
        }
        
        // Check if professional name already exists
        if ($professionalModel->professionalNameExists($name)) {
            json_response(['success' => false, 'message' => 'El nombre del profesional ya existe'], 400);
        }
        
        // Add new professional
        $professionalId = $professionalModel->addProfessional($name, $status, $statusFrom, $statusTo);
        
        if ($professionalId) {
            $professional = $professionalModel->getProfessionalById($professionalId);
            json_response(['success' => true, 'message' => 'Profesional agregado exitosamente', 'data' => $professional]);
        } else {
            json_response(['success' => false, 'message' => 'Error al agregar el profesional'], 500);
        }
        break;
        
    case 'PUT':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            json_response(['success' => false, 'message' => 'ID de profesional inválido'], 400);
        }
        
        $id = (int) $data['id'];
        
        // Check if professional exists
        $professional = $professionalModel->getProfessionalById($id);
        if (!$professional) {
            json_response(['success' => false, 'message' => 'Profesional no encontrado'], 404);
        }
        
        // Check update type - full update or status update
        if (isset($data['status_update']) && $data['status_update'] === true) {
            // Status update only
            if (!isset($data['status'])) {
                json_response(['success' => false, 'message' => 'El estado es requerido'], 400);
            }
            
            $status = sanitize_data($data['status']);
            $statusFrom = null;
            $statusTo = null;
            
            // Validate dates if status is not 'Vigente'
            if ($status !== 'Vigente') {
                if (isset($data['status_from']) && !empty($data['status_from'])) {
                    if (!validate_date($data['status_from'])) {
                        json_response(['success' => false, 'message' => 'Formato de fecha inicial inválido'], 400);
                    }
                    $statusFrom = $data['status_from'];
                }
                
                if (isset($data['status_to']) && !empty($data['status_to'])) {
                    if (!validate_date($data['status_to'])) {
                        json_response(['success' => false, 'message' => 'Formato de fecha final inválido'], 400);
                    }
                    $statusTo = $data['status_to'];
                }
                
                // Check date range
                if ($statusFrom && $statusTo && strtotime($statusFrom) > strtotime($statusTo)) {
                    json_response(['success' => false, 'message' => 'La fecha inicial debe ser anterior a la fecha final'], 400);
                }
            }
            
            // Update professional status
            if ($professionalModel->updateProfessionalStatus($id, $status, $statusFrom, $statusTo)) {
                $updatedProfessional = $professionalModel->getProfessionalById($id);
                json_response(['success' => true, 'message' => 'Estado actualizado exitosamente', 'data' => $updatedProfessional]);
            } else {
                json_response(['success' => false, 'message' => 'Error al actualizar el estado'], 500);
            }
        } else {
            // Full update
            if (!isset($data['name']) || empty(trim($data['name']))) {
                json_response(['success' => false, 'message' => 'El nombre del profesional es requerido'], 400);
            }
            
            $name = sanitize_data($data['name']);
            $status = isset($data['status']) ? sanitize_data($data['status']) : 'Vigente';
            $statusFrom = null;
            $statusTo = null;
            
            // Validate dates if status is not 'Vigente'
            if ($status !== 'Vigente') {
                if (isset($data['status_from']) && !empty($data['status_from'])) {
                    if (!validate_date($data['status_from'])) {
                        json_response(['success' => false, 'message' => 'Formato de fecha inicial inválido'], 400);
                    }
                    $statusFrom = $data['status_from'];
                }
                
                if (isset($data['status_to']) && !empty($data['status_to'])) {
                    if (!validate_date($data['status_to'])) {
                        json_response(['success' => false, 'message' => 'Formato de fecha final inválido'], 400);
                    }
                    $statusTo = $data['status_to'];
                }
                
                // Check date range
                if ($statusFrom && $statusTo && strtotime($statusFrom) > strtotime($statusTo)) {
                    json_response(['success' => false, 'message' => 'La fecha inicial debe ser anterior a la fecha final'], 400);
                }
            }
            
            // Check if new name already exists
            if ($name !== $professional['name'] && $professionalModel->professionalNameExists($name, $id)) {
                json_response(['success' => false, 'message' => 'El nombre del profesional ya existe'], 400);
            }
            
            // Update professional
            if ($professionalModel->updateProfessional($id, $name, $status, $statusFrom, $statusTo)) {
                $updatedProfessional = $professionalModel->getProfessionalById($id);
                json_response(['success' => true, 'message' => 'Profesional actualizado exitosamente', 'data' => $updatedProfessional]);
            } else {
                json_response(['success' => false, 'message' => 'Error al actualizar el profesional'], 500);
            }
        }
        break;
        
    case 'DELETE':
        // Get input data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate data
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            json_response(['success' => false, 'message' => 'ID de profesional inválido'], 400);
        }
        
        $id = (int) $data['id'];
        
        // Check if professional exists
        $professional = $professionalModel->getProfessionalById($id);
        if (!$professional) {
            json_response(['success' => false, 'message' => 'Profesional no encontrado'], 404);
        }
        
        // Delete professional
        if ($professionalModel->deleteProfessional($id)) {
            json_response(['success' => true, 'message' => 'Profesional eliminado exitosamente']);
        } else {
            json_response(['success' => false, 'message' => 'Error al eliminar el profesional'], 500);
        }
        break;
        
    default:
        json_response(['success' => false, 'message' => 'Método no permitido'], 405);
}